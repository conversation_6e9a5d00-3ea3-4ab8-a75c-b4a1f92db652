/**
 * @file matching-engine.js - 增强匹配引擎
 * @description 提供模糊匹配、同义词匹配、语义相似度计算等高级匹配功能
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 */

/**
 * @class EnhancedMatchingEngine - 增强匹配引擎
 * @description 提供模糊匹配、同义词匹配、语义相似度计算等高级匹配功能
 */
class EnhancedMatchingEngine {
    constructor() {
        // 同义词词典 - 支持中英文
        this.synonymDict = {
            // 车型同义词
            'sedan': ['轿车', '小车', '私家车', 'car', 'saloon'],
            'suv': ['越野车', '运动型多用途车', 'sport utility vehicle'],
            'mpv': ['商务车', '多用途车', 'multi-purpose vehicle', '面包车'],
            'van': ['面包车', '厢式车', '货车', 'minivan'],
            'bus': ['大巴', '巴士', '客车', 'coach'],
            'luxury': ['豪华', '高端', '奢华', 'premium', 'deluxe', 'vip'],
            
            // 服务类型同义词
            'pickup': ['接机', '接送', '迎接', 'pick up', 'collect'],
            'dropoff': ['送机', '送达', '护送', 'drop off', 'deliver'],
            'charter': ['包车', '租车', '专车', 'private hire', 'exclusive'],
            'transfer': ['转移', '接驳', '中转', 'transport'],
            
            // 地点同义词
            'airport': ['机场', '航站楼', '候机楼', 'terminal'],
            'hotel': ['酒店', '宾馆', '旅馆', 'resort', 'accommodation'],
            'mall': ['商场', '购物中心', 'shopping center', 'plaza'],
            
            // 时间同义词
            'urgent': ['紧急', '急需', '马上', 'asap', 'immediately'],
            'flexible': ['灵活', '可调整', '弹性', 'adjustable']
        };
        
        // 拼音匹配表（扩展版）
        this.pinyinMap = {
            // 基础拼音
            'jie': '接', 'song': '送', 'che': '车', 'ji': '机',
            'bao': '包', 'hao': '豪', 'hua': '华', 'shang': '商',
            'wu': '务', 'jing': '经', 'ji': '济', 'shu': '舒',
            'shi': '适', 'da': '大', 'xiao': '小', 'zhong': '中',
            // 扩展拼音
            'jie机': '接机', 'song机': '送机', 'bao车': '包车', 
            'lv游': '旅游', 'jing点': '景点', 'hao华': '豪华',
            'shang务': '商务', 'an全': '安全', 'kuai捷': '快捷',
            'wen度': '温度', 'su度': '速度', 'gao铁': '高铁',
            'fei机': '飞机', 'huo车': '火车', 'lu线': '路线'
        };
        
        // 缩写匹配表
        this.abbreviationMap = {
            // 英文缩写
            'suv': 'sport utility vehicle',
            'mpv': 'multi-purpose vehicle', 
            'vip': 'very important person',
            'api': 'application programming interface',
            'ota': 'online travel agency',
            'gps': 'global positioning system',
            'ac': 'air conditioning',
            'atm': 'automated teller machine',
            'kl': 'kuala lumpur',
            'pj': 'petaling jaya',
            'sg': 'singapore',
            
            // 中文缩写
            '北京': 'bj', '上海': 'sh', '广州': 'gz', '深圳': 'sz',
            '成都': 'cd', '杭州': 'hz', '南京': 'nj', '武汉': 'wh',
            '西安': 'xa', '重庆': 'cq', '天津': 'tj', '青岛': 'qd'
        };
        
        // 语义相似度词汇表（扩展版）
        this.semanticGroups = {
            transportation: {
                vehicles: ['车', 'car', 'vehicle', 'automobile', '汽车', '车辆'],
                types: ['sedan', 'suv', 'mpv', 'van', 'bus', '轿车', '越野车', '商务车', '面包车', '巴士'],
                actions: ['drive', 'ride', 'travel', '驾驶', '乘坐', '出行', '运输']
            },
            service: {
                types: ['pickup', 'dropoff', 'charter', 'transfer', '接送', '包车', '转乘', '服务'],
                quality: ['luxury', 'premium', 'standard', 'economy', '豪华', '高级', '标准', '经济'],
                time: ['urgent', 'immediate', 'scheduled', '紧急', '立即', '预约', '定时']
            },
            location: {
                airport: ['airport', 'terminal', 'runway', '机场', '航站楼', '候机楼'],
                hotel: ['hotel', 'resort', 'accommodation', '酒店', '度假村', '住宿'],
                attraction: ['attraction', 'sightseeing', 'tour', '景点', '观光', '旅游']
            }
        };
        
        // 匹配权重配置（增强版）
        this.matchWeights = {
            exactMatch: 1.0,        // 精确匹配
            synonymMatch: 0.9,      // 同义词匹配
            pinyinMatch: 0.85,      // 拼音匹配
            abbreviationMatch: 0.8, // 缩写匹配
            fuzzyMatch: 0.75,       // 模糊匹配
            semanticMatch: 0.7,     // 语义匹配
            partialMatch: 0.6,      // 部分匹配
            contextMatch: 0.5,      // 上下文匹配
            soundexMatch: 0.4       // 音似匹配
        };
    }
    
    /**
     * @function calculateStringDistance - 计算字符串编辑距离
     * @param {string} str1 - 字符串1
     * @param {string} str2 - 字符串2
     * @returns {number} 编辑距离
     */
    calculateStringDistance(str1, str2) {
        if (!str1 || !str2) return Infinity;
        
        const len1 = str1.length;
        const len2 = str2.length;
        
        // 创建距离矩阵
        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
        
        // 初始化第一行和第一列
        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;
        
        // 计算编辑距离
        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,      // 删除
                    matrix[i][j - 1] + 1,      // 插入
                    matrix[i - 1][j - 1] + cost // 替换
                );
            }
        }
        
        return matrix[len1][len2];
    }
    
    /**
     * @function calculateSimilarity - 计算相似度评分
     * @param {string} source - 源字符串
     * @param {string} target - 目标字符串
     * @returns {number} 相似度评分 (0.0-1.0)
     */
    calculateSimilarity(source, target) {
        if (!source || !target) return 0;
        
        const src = source.toLowerCase().trim();
        const tgt = target.toLowerCase().trim();
        
        // 精确匹配
        if (src === tgt) return this.matchWeights.exactMatch;
        
        // 包含匹配
        if (src.includes(tgt) || tgt.includes(src)) {
            const longerLength = Math.max(src.length, tgt.length);
            const shorterLength = Math.min(src.length, tgt.length);
            return this.matchWeights.partialMatch * (shorterLength / longerLength);
        }
        
        // 编辑距离匹配
        const distance = this.calculateStringDistance(src, tgt);
        const maxLength = Math.max(src.length, tgt.length);
        if (maxLength === 0) return 0;
        
        const similarity = 1 - (distance / maxLength);
        return similarity * this.matchWeights.fuzzyMatch;
    }
    
    /**
     * @function findSynonyms - 查找同义词
     * @param {string} word - 输入词汇
     * @returns {string[]} 同义词列表
     */
    findSynonyms(word) {
        const normalizedWord = word.toLowerCase().trim();
        const synonyms = [];
        
        // 直接查找
        if (this.synonymDict[normalizedWord]) {
            synonyms.push(...this.synonymDict[normalizedWord]);
        }
        
        // 反向查找
        for (const [key, values] of Object.entries(this.synonymDict)) {
            if (values.some(synonym => synonym.toLowerCase() === normalizedWord)) {
                synonyms.push(key, ...values);
            }
        }
        
        // 去重并返回
        return [...new Set(synonyms)].filter(syn => syn.toLowerCase() !== normalizedWord);
    }
    
    /**
     * @function enhancedKeywordMatch - 增强关键词匹配算法 v2.0
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 匹配结果，包含多种匹配策略的结果
     */
    enhancedKeywordMatch(text, keyword) {
        if (!text || !keyword) return { matched: false, score: 0 };
        
        const normalizedText = text.toLowerCase().trim();
        const normalizedKeyword = keyword.toLowerCase().trim();
        
        // 存储所有匹配结果
        const matchResults = [];
        
        // 1. 精确匹配
        if (normalizedText.includes(normalizedKeyword)) {
            matchResults.push({
                matched: true,
                score: this.matchWeights.exactMatch,
                method: 'exact_match',
                matchedTerm: normalizedKeyword,
                confidence: 1.0
            });
        }
        
        // 2. 同义词匹配
        const synonyms = this.findSynonyms(normalizedKeyword);
        for (const synonym of synonyms) {
            if (normalizedText.includes(synonym.toLowerCase())) {
                matchResults.push({
                    matched: true,
                    score: this.matchWeights.synonymMatch,
                    method: 'synonym_match',
                    matchedTerm: synonym,
                    originalKeyword: normalizedKeyword,
                    confidence: 0.9
                });
                break; // 只取第一个匹配的同义词
            }
        }
        
        // 3. 拼音匹配（新增）
        const pinyinResult = this.performPinyinMatch(normalizedText, normalizedKeyword);
        if (pinyinResult.matched) {
            matchResults.push({
                ...pinyinResult,
                score: this.matchWeights.pinyinMatch,
                confidence: 0.85
            });
        }
        
        // 4. 缩写匹配（新增）
        const abbreviationResult = this.performAbbreviationMatch(normalizedText, normalizedKeyword);
        if (abbreviationResult.matched) {
            matchResults.push({
                ...abbreviationResult,
                score: this.matchWeights.abbreviationMatch,
                confidence: 0.8
            });
        }
        
        // 5. 语义匹配（新增）
        const semanticResult = this.performSemanticMatch(normalizedText, normalizedKeyword);
        if (semanticResult.matched) {
            matchResults.push({
                ...semanticResult,
                score: this.matchWeights.semanticMatch,
                confidence: 0.7
            });
        }
        
        // 选择最佳匹配结果
        if (matchResults.length > 0) {
            // 按评分排序，选择最佳结果
            matchResults.sort((a, b) => b.score - a.score);
            const bestResult = matchResults[0];
            
            // 添加所有匹配方法的信息
            bestResult.allMatches = matchResults.map(r => ({
                method: r.method,
                score: r.score,
                confidence: r.confidence
            }));
            
            return bestResult;
        }
        
        return { matched: false, score: 0, allMatches: [] };
    }

    /**
     * @function performPinyinMatch - 执行拼音匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 拼音匹配结果
     */
    performPinyinMatch(text, keyword) {
        // 检查拼音映射
        for (const [pinyin, chinese] of Object.entries(this.pinyinMap)) {
            // 拼音转中文匹配
            if (keyword.includes(pinyin) && text.includes(chinese)) {
                return {
                    matched: true,
                    method: 'pinyin_to_chinese_match',
                    matchedTerm: chinese,
                    pinyinTerm: pinyin,
                    originalKeyword: keyword
                };
            }

            // 中文转拼音匹配
            if (keyword.includes(chinese) && text.includes(pinyin)) {
                return {
                    matched: true,
                    method: 'chinese_to_pinyin_match',
                    matchedTerm: pinyin,
                    chineseTerm: chinese,
                    originalKeyword: keyword
                };
            }
        }

        return { matched: false };
    }

    /**
     * @function performAbbreviationMatch - 执行缩写匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 缩写匹配结果
     */
    performAbbreviationMatch(text, keyword) {
        // 检查缩写映射
        for (const [abbr, full] of Object.entries(this.abbreviationMap)) {
            // 缩写转全称匹配
            if (keyword.includes(abbr) && text.includes(full.toLowerCase())) {
                return {
                    matched: true,
                    method: 'abbreviation_to_full_match',
                    matchedTerm: full,
                    abbreviation: abbr,
                    originalKeyword: keyword
                };
            }

            // 全称转缩写匹配
            if (keyword.includes(full.toLowerCase()) && text.includes(abbr)) {
                return {
                    matched: true,
                    method: 'full_to_abbreviation_match',
                    matchedTerm: abbr,
                    fullTerm: full,
                    originalKeyword: keyword
                };
            }
        }

        return { matched: false };
    }

    /**
     * @function performSemanticMatch - 执行语义匹配
     * @param {string} text - 搜索文本
     * @param {string} keyword - 关键词
     * @returns {object} 语义匹配结果
     */
    performSemanticMatch(text, keyword) {
        // 遍历语义组
        for (const [category, groups] of Object.entries(this.semanticGroups)) {
            for (const [groupName, words] of Object.entries(groups)) {
                // 检查关键词是否在语义组中
                const keywordInGroup = words.some(word =>
                    keyword.includes(word.toLowerCase()));

                if (keywordInGroup) {
                    // 检查文本中是否有同组的其他词汇
                    for (const word of words) {
                        if (text.includes(word.toLowerCase()) &&
                            !keyword.includes(word.toLowerCase())) {
                            return {
                                matched: true,
                                method: 'semantic_group_match',
                                matchedTerm: word,
                                semanticCategory: category,
                                semanticGroup: groupName,
                                originalKeyword: keyword
                            };
                        }
                    }
                }
            }
        }

        return { matched: false };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedMatchingEngine;
} else if (typeof window !== 'undefined') {
    window.EnhancedMatchingEngine = EnhancedMatchingEngine;
}
