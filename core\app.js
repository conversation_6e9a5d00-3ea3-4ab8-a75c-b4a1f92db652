/**
 * @file app.js - OTA订单处理系统主应用（精简版）
 * @description 模块化重构后的主应用控制器，协调各个管理器模块
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 * @dependencies app-state.js, ui-manager.js, event-manager.js, order-processor.js, data-consistency-manager.js, error-recovery-manager.js
 */

/**
 * @class OTAOrderApp - OTA订单处理主应用类（精简版）
 * @description 协调各个模块，管理应用的整体流程和状态
 */
class OTAOrderApp {
    /**
     * @function constructor - 构造函数
     * @description 初始化主应用和所有管理器模块
     */
    constructor() {
        // 获取应用状态实例
        this.appState = window.appState;
        
        // 初始化服务层
        this.apiService = new ApiService(this.appState);
        this.llmService = new LLMService();
        this.orderParser = new OrderParser(this.llmService);
        this.imageService = new ImageService();
        
        // 初始化管理器层
        this.uiManager = new UIManager(this.appState);
        this.eventManager = new EventManager(this);
        this.orderProcessor = new OrderProcessor(this.appState, this.orderParser, window.smartSelection);
        this.dataConsistencyManager = new DataConsistencyManager(this.appState, this.apiService);
        this.errorRecoveryManager = new ErrorRecoveryManager(this.appState, this.apiService, this.dataConsistencyManager);
        
        // 应用状态
        this.isInitialized = false;
        
        logger.info('应用', 'OTAOrderApp实例创建完成', { version: 'v4.0.1' });
    }

    /**
     * @function initialize - 初始化应用
     * @description 协调各个模块的初始化流程
     */
    async initialize() {
        if (this.isInitialized) {
            logger.debug('应用', '应用已初始化，跳过重复初始化');
            return;
        }

        logger.info('应用', '开始初始化OTA订单处理系统');

        try {
            // 1. 初始化UI组件
            this.uiManager.initializeUI();

            // 2. 绑定事件监听器
            this.eventManager.bindEventListeners();
            this.eventManager.bindUserSwitchEvent();

            // 3. 检查登录状态
            await this.checkAuthStatus();

            // 4. 初始化LLM连接检测
            this.initializeLLMStatus();

            // 5. 加载系统数据
            await this.loadSystemData();

            // 6. 初始化智能选择服务
            this.initializeSmartSelection();

            // 7. 初始化地址搜索服务
            this.initializeAddressSearchService();

            // 8. 启动数据验证调度
            this.dataConsistencyManager.scheduleValidation();

            this.isInitialized = true;
            logger.success('应用', '系统初始化完成');

        } catch (error) {
            logger.error('应用', '系统初始化失败', { error: error.message });
            this.uiManager.showError('系统初始化失败: ' + error.message);
            throw error;
        }
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     * @description 检查用户登录状态并更新UI
     */
    async checkAuthStatus() {
        logger.info('应用', '检查认证状态');

        if (this.appState.token && this.appState.userInfo) {
            try {
                // 验证token有效性
                const isValid = await this.apiService.validateToken();
                if (isValid) {
                    this.uiManager.hideLoginModal();
                    this.uiManager.updateConnectionStatus();
                    logger.success('应用', '用户已登录', { 
                        user: this.appState.userInfo.email 
                    });
                } else {
                    throw new Error('Token验证失败');
                }
            } catch (error) {
                logger.warn('应用', 'Token验证失败，需要重新登录', error);
                this.appState.clearAuth();
                this.uiManager.showLoginModal();
            }
        } else {
            logger.debug('应用', '用户未登录');
            this.uiManager.showLoginModal();
        }
    }

    /**
     * @function initializeLLMStatus - 初始化LLM状态
     * @description 检查LLM服务连接状态
     */
    initializeLLMStatus() {
        logger.info('应用', '初始化LLM状态检查');
        
        // 异步检查LLM连接状态
        this.checkLLMConnections().catch(error => {
            logger.warn('应用', 'LLM连接检查失败', error);
        });
    }

    /**
     * @function checkLLMConnections - 检查LLM连接
     * @description 检查所有LLM服务的连接状态
     */
    async checkLLMConnections() {
        try {
            // 检查Gemini连接
            const geminiStatus = await this.llmService.checkGeminiConnection();
            
            // 更新UI状态
            this.uiManager.updateLLMStatusUI(geminiStatus);
            
            logger.info('应用', 'LLM连接状态检查完成', { geminiStatus });
            
        } catch (error) {
            logger.error('应用', 'LLM连接检查失败', error);
        }
    }

    /**
     * @function loadSystemData - 加载系统数据
     * @description 加载所有必需的系统数据
     */
    async loadSystemData() {
        if (!this.appState.token) {
            logger.debug('应用', '未登录，跳过系统数据加载');
            return;
        }

        logger.info('应用', '开始加载系统数据');

        try {
            this.uiManager.showLoading('加载系统数据...');

            // 并行加载所有系统数据
            await Promise.all([
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ]);

            // 更新UI选择器
            this.uiManager.updateUISelectors();

            logger.success('应用', '系统数据加载完成');

        } catch (error) {
            logger.error('应用', '系统数据加载失败', error);
            this.uiManager.showError('系统数据加载失败: ' + error.message);
        } finally {
            this.uiManager.hideLoading();
        }
    }

    /**
     * @function initializeSmartSelection - 初始化智能选择服务
     * @description 初始化智能选择服务并设置数据
     */
    initializeSmartSelection() {
        if (window.smartSelection) {
            try {
                window.smartSelection.updateSystemData(
                    this.appState.backendUsers,
                    this.appState.subCategories,
                    this.appState.carTypes
                );
                logger.success('应用', '智能选择服务初始化完成');
            } catch (error) {
                logger.error('应用', '智能选择服务初始化失败', error);
            }
        } else {
            logger.warn('应用', '智能选择服务未加载');
        }
    }

    /**
     * @function initializeAddressSearchService - 初始化地址搜索服务
     * @description 初始化地址搜索服务
     */
    initializeAddressSearchService() {
        if (window.addressSearchService) {
            try {
                window.addressSearchService.initialize();
                logger.success('应用', '地址搜索服务初始化完成');
            } catch (error) {
                logger.error('应用', '地址搜索服务初始化失败', error);
            }
        } else {
            logger.warn('应用', '地址搜索服务未加载');
        }
    }

    /**
     * @function handleLogin - 处理用户登录
     * @description 处理用户登录表单提交
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            this.uiManager.showError('请输入邮箱和密码');
            return;
        }

        logger.info('应用', '处理用户登录', { email });

        try {
            this.uiManager.showLoading('登录中...');

            const result = await this.apiService.login(email, password);
            
            if (result.success) {
                // 设置认证信息
                this.appState.setToken(result.token);
                this.appState.setUserInfo(result.user);
                
                // 隐藏登录模态框
                this.uiManager.hideLoginModal();
                this.uiManager.updateConnectionStatus();
                
                // 加载系统数据
                await this.loadSystemData();
                
                // 初始化智能选择服务
                this.initializeSmartSelection();
                
                this.uiManager.showSuccess('登录成功');
                logger.success('应用', '用户登录成功', { user: result.user.email });
                
            } else {
                throw new Error(result.error || '登录失败');
            }

        } catch (error) {
            logger.error('应用', '登录失败', error);
            this.uiManager.showError('登录失败: ' + error.message);
        } finally {
            this.uiManager.hideLoading();
        }
    }

    /**
     * @function handleLogout - 处理用户登出
     * @description 处理用户登出操作
     */
    handleLogout() {
        logger.info('应用', '处理用户登出');
        
        try {
            // 清除认证信息
            this.appState.clearAuth();
            
            // 清空UI显示
            this.uiManager.clearUIDisplays();
            
            // 重置智能选择服务
            if (window.smartSelection) {
                window.smartSelection.resetToDefaults();
            }
            
            // 显示登录模态框
            this.uiManager.showLoginModal();
            this.uiManager.updateConnectionStatus();
            
            this.uiManager.showInfo('已安全登出');
            logger.success('应用', '用户登出完成');
            
        } catch (error) {
            logger.error('应用', '登出处理失败', error);
            this.uiManager.showError('登出失败: ' + error.message);
        }
    }

    /**
     * @function handleUserSwitch - 处理用户切换
     * @description 处理用户账号切换事件
     * @param {object} detail - 切换详情
     */
    async handleUserSwitch(detail) {
        logger.info('应用', '处理用户切换事件', detail);
        
        try {
            // 清空UI显示
            this.uiManager.clearUIDisplays();
            
            // 重置智能选择服务
            if (window.smartSelection) {
                window.smartSelection.resetToDefaults();
            }
            
            // 显示切换提示
            const confirmed = await this.showUserSwitchConfirmation(detail);
            if (confirmed) {
                // 重新加载系统数据
                await this.loadSystemData();
                
                // 更新UI选择器
                this.uiManager.updateUISelectors();
                
                // 重新初始化智能选择服务
                this.initializeSmartSelection();
                
                this.uiManager.showSuccess('用户切换完成');
                logger.success('应用', '用户切换处理完成');
            }
        } catch (error) {
            logger.error('应用', '用户切换处理失败', error);
            this.uiManager.showError('用户切换失败: ' + error.message);
        }
    }

    /**
     * @function showUserSwitchConfirmation - 显示用户切换确认
     * @description 显示用户切换确认对话框
     * @param {object} switchInfo - 切换信息
     * @returns {Promise<boolean>} 用户确认结果
     */
    async showUserSwitchConfirmation(switchInfo) {
        return new Promise((resolve) => {
            const message = `检测到账号切换：\n从 ${switchInfo.oldUser} 切换到 ${switchInfo.newUser}\n\n系统将清理旧用户数据并重新加载。是否继续？`;
            
            if (confirm(message)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }

    /**
     * @function processUploadedFiles - 处理上传的文件
     * @description 处理用户上传的图片文件
     * @param {FileList} files - 文件列表
     */
    async processUploadedFiles(files) {
        if (!files || files.length === 0) return;

        logger.info('应用', '处理上传文件', { fileCount: files.length });

        try {
            this.uiManager.showLoading('处理图片文件...');

            for (const file of files) {
                if (file.type.startsWith('image/')) {
                    const extractedText = await this.imageService.extractTextFromImage(file);
                    if (extractedText) {
                        // 将提取的文本填入订单文本框
                        const textArea = document.getElementById('orderText');
                        if (textArea) {
                            textArea.value = extractedText;
                        }
                        
                        this.uiManager.showSuccess('图片文字提取成功');
                        logger.success('应用', '图片文字提取完成', { 
                            textLength: extractedText.length 
                        });
                        break; // 只处理第一个图片文件
                    }
                }
            }

        } catch (error) {
            logger.error('应用', '文件处理失败', error);
            this.uiManager.showError('文件处理失败: ' + error.message);
        } finally {
            this.uiManager.hideLoading();
        }
    }

    /**
     * @function showError - 显示错误消息
     * @description 显示错误消息的便捷方法
     * @param {string} message - 错误消息
     */
    showError(message) {
        this.uiManager.showError(message);
    }

    /**
     * @function showSuccess - 显示成功消息
     * @description 显示成功消息的便捷方法
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        this.uiManager.showSuccess(message);
    }
}

// 创建全局实例
window.app = new OTAOrderApp();

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await window.app.initialize();
        logger.success('应用', 'OTA订单处理系统启动完成');
    } catch (error) {
        logger.error('应用', '系统启动失败', error);
    }
});

logger.info('模块', 'OTAOrderApp主应用模块加载完成', { version: 'v4.0.1' });
