/**
 * @file main.js - 智能选择服务主类
 * @description 实现基于订单内容的智能ID选择功能，包括车型识别和自动选择sub_category_id、car_type_id、incharge_by_backend_user_id
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2025-01-06
 */

/**
 * @class SmartSelectionService - 智能选择服务主类
 * @description 实现基于订单内容的智能ID选择功能，包括车型识别和自动选择sub_category_id、car_type_id、incharge_by_backend_user_id
 */
class SmartSelectionService {
    constructor() {
        this.config = window.SYSTEM_CONFIG?.SMART_SELECTION || {};
        this.logger = window.logger || console;
        this.initialized = false;
        
        // 初始化增强组件 v3.1.0
        this.enhancedMatchingEngine = new EnhancedMatchingEngine();
        this.learningEngine = new IntelligentLearningEngine();
        this.accuracyCalculator = new DynamicAccuracyCalculator(this.learningEngine);
        
        // 初始化动态API同步管理器 (新增)
        this.apiSyncManager = new DynamicApiSyncManager();
        
        // 算法版本信息 (更新)
        this.version = '3.1.0';
        this.algorithmEnhancements = {
            fuzzyMatching: true,
            synonymSupport: true,
            learningEngine: true,
            dynamicScoring: true,
            contextAnalysis: true,
            dynamicApiSync: true,      // 新增
            pinyinMatching: true,      // 新增
            abbreviationMatch: true,   // 新增
            semanticMatching: true,    // 新增
            soundexMatching: true      // 新增
        };
        
        // 添加API同步状态监听器
        this.apiSyncManager.addStatusListener((status, data) => {
            this.handleApiSyncStatusChange(status, data);
        });
        
        // 初始化映射表
        this.initializeMappingTables();
    }
    
    /**
     * @function initializeMappingTables - 初始化映射表
     */
    initializeMappingTables() {
        // 车型识别映射表 - 基于真实API数据 (更新于2024年12月)
        this.vehicleTypeMapping = {
            // 基于乘客数量的车型映射 - 根据最新API数据更新座位数
            passengerCount: {
                1: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                2: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                3: { type: 'compact', id: 5, name: 'Compact 5 Seater', seats: 4 },
                4: { type: 'comfort', id: 6, name: 'Comfort 5 Seater', seats: 4 },
                5: { type: 'comfort', id: 6, name: 'Comfort 5 Seater', seats: 4 },
                6: { type: 'mpv', id: 16, name: 'Standard Size MPV', seats: 6 },
                7: { type: 'suv', id: 15, name: 'Mid Size SUV', seats: 7 },
                8: { type: 'mpv', id: 16, name: 'Standard Size MPV', seats: 6 },
                9: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 },
                10: { type: 'van', id: 20, name: '10 Seater MPV / Van', seats: 9 }
            },
            // 增强的车型关键词映射 - 支持更多车型识别和智能匹配
            keywords: {
                // 经济型轿车关键词 - ID: 5, 座位数: 4
                'sedan': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                'economy': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                'compact': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '经济': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '轿车': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '小车': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                '四座': { id: 5, name: 'Compact 5 Seater', category: 'sedan', seats: 4 },
                
                // 舒适型车辆关键词 - ID: 6, 座位数: 4
                'comfort': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '舒适': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '5座': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 },
                '五座': { id: 6, name: 'Comfort 5 Seater', category: 'comfort', seats: 4 }
            }
        };
        
        // 服务类型映射表 - 基于最新API数据更新
        this.serviceTypeMapping = {
            // 机场服务
            '接机': { id: 7, name: 'Pickup', category: 'Airport' },
            '送机': { id: 8, name: 'Dropoff', category: 'Airport' },
            '点对点': { id: 7, name: 'Pickup', category: 'Airport' },
            '机场转乘': { id: 7, name: 'Pickup', category: 'Airport' },
            'pickup': { id: 7, name: 'Pickup', category: 'Airport' },
            'dropoff': { id: 8, name: 'Dropoff', category: 'Airport' },
            'airport_pickup': { id: 7, name: 'Pickup', category: 'Airport' },
            'airport_dropoff': { id: 8, name: 'Dropoff', category: 'Airport' },
            
            // 包车服务
            '包车': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'charter': { id: 9, name: 'KL to genting', category: 'Chartered' },
            'chartered': { id: 9, name: 'KL to genting', category: 'Chartered' },
            
            // 默认服务
            'default': { id: 16, name: 'default', category: 'default' }
        };
        
        // 后台用户映射表 - 基于最新API数据更新
        this.backendUserMapping = {
            // 超级管理员
            'super_admin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'admin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'superadmin': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            '超级管理员': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            
            // 管理员
            'manager': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            'mgr': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            '管理员': { id: 2, name: 'Manager', role: 'manager', phone: '60123456790' },
            
            // 默认分配给超级管理员
            'default': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'fallback': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'gomyhire': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' },
            'chong_dealer': { id: 1, name: 'Super Admin', role: 'super_admin', phone: '60123456789' }
        };
    }
    
    /**
     * @function initialize - 初始化智能选择服务
     * @returns {Promise<boolean>} 初始化结果
     */
    async initialize() {
        try {
            this.logger.info('SmartSelection', '初始化智能选择服务');
            
            // 从应用状态获取最新的选择器数据
            await this.updateMappingFromAppState();
            
            this.initialized = true;
            this.logger.info('SmartSelection', '智能选择服务初始化完成');
            
            return true;
        } catch (error) {
            this.logger.error('SmartSelection', '初始化失败', error);
            return false;
        }
    }
    
    /**
     * @function updateMappingFromAppState - 从应用状态更新映射表
     */
    async updateMappingFromAppState() {
        try {
            const app = window.app;
            if (!app || !app.appState) {
                this.logger.warn('SmartSelection', '应用状态不可用，使用默认映射');
                return;
            }
            
            // 动态车型映射系统 - 根据API返回的车型数据自动适配
            if (app.appState.carTypes && app.appState.carTypes.length > 0) {
                const vehicleMappingStrategy = this.createDynamicVehicleMapping(app.appState.carTypes);
                this.vehicleTypeMapping = vehicleMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态车型映射已更新', {
                    strategy: vehicleMappingStrategy.strategy,
                    totalVehicles: app.appState.carTypes.length,
                    passengerRanges: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                    keywordMappings: Object.keys(this.vehicleTypeMapping.keywords).length,
                    vehicleCategories: vehicleMappingStrategy.categories
                });
            }
            
            // 动态服务类型映射系统 - 根据API返回的子分类数据自动适配
            if (app.appState.subCategories && app.appState.subCategories.length > 0) {
                const serviceMappingStrategy = this.createDynamicServiceMapping(app.appState.subCategories);
                this.serviceTypeMapping = serviceMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态服务类型映射已更新', {
                    strategy: serviceMappingStrategy.strategy,
                    totalServices: app.appState.subCategories.length,
                    serviceMappings: Object.keys(this.serviceTypeMapping).length,
                    serviceCategories: serviceMappingStrategy.categories,
                    defaultService: serviceMappingStrategy.defaultService
                });
            }
            
            // 动态后台用户映射系统，能够根据API返回的不同ID自动适配并选择合适的用户
            if (app.appState.backendUsers && app.appState.backendUsers.length > 0) {
                // 创建动态用户映射策略
                const userMappingStrategy = this.createDynamicUserMapping(app.appState.backendUsers);
                
                // 应用映射策略
                this.backendUserMapping = userMappingStrategy.mapping;
                
                this.logger.info('SmartSelection', '动态后台用户映射已更新', {
                    strategy: userMappingStrategy.strategy,
                    defaultUserId: userMappingStrategy.defaultUser.id,
                    defaultUserName: userMappingStrategy.defaultUser.name,
                    defaultUserRole: userMappingStrategy.defaultUser.role,
                    totalUsers: app.appState.backendUsers.length,
                    mappingKeys: Object.keys(this.backendUserMapping).length,
                    userProfiles: userMappingStrategy.userProfiles
                });
            }
            
            this.logger.debug('SmartSelection', '所有映射表更新完成', {
                vehicleTypes: Object.keys(this.vehicleTypeMapping.passengerCount).length,
                serviceTypes: Object.keys(this.serviceTypeMapping).length,
                backendUsers: Object.keys(this.backendUserMapping).length
            });
            
        } catch (error) {
            this.logger.error('SmartSelection', '更新映射表失败', error);
        }
    }
    
    /**
     * @function handleApiSyncStatusChange - 处理API同步状态变化
     * @param {string} status - 同步状态
     * @param {object} data - 同步数据
     */
    handleApiSyncStatusChange(status, data) {
        this.logger.debug('SmartSelection', 'API同步状态变化', { status, data });

        if (status === 'sync_completed') {
            // API同步完成，更新映射表
            this.updateMappingFromAppState();
        }
    }

    /**
     * @function createDynamicVehicleMapping - 创建动态车型映射策略
     * @param {Array} carTypes - API返回的车型列表
     * @returns {Object} 车型映射策略对象
     */
    createDynamicVehicleMapping(carTypes) {
        try {
            // 车型分类模式识别
            const vehiclePatterns = {
                compact: /compact|小型|经济|economy/i,
                comfort: /comfort|舒适|standard/i,
                luxury: /luxury|豪华|premium|alphard|velfire|vellfire/i,
                suv: /suv|越野|sport.*utility/i,
                mpv: /mpv|multi.*purpose|商务/i,
                van: /van|面包|客车/i,
                bus: /bus|巴士|大巴|coach/i,
                minibus: /mini.*bus|中巴|小巴/i
            };

            // 座位数范围定义
            const seatRanges = {
                micro: { min: 1, max: 2, category: 'micro' },
                compact: { min: 3, max: 5, category: 'compact' },
                standard: { min: 6, max: 8, category: 'standard' },
                large: { min: 9, max: 15, category: 'large' },
                xlarge: { min: 16, max: 30, category: 'xlarge' },
                bus: { min: 31, max: 100, category: 'bus' }
            };

            // 按优先级排序车型
            const sortedCarTypes = carTypes.sort((a, b) => (a.priority || 999) - (b.priority || 999));

            // 分析车型配置文件
            const vehicleProfiles = sortedCarTypes.map(carType => {
                const profile = {
                    id: carType.id,
                    type: carType.type || `Vehicle_${carType.id}`,
                    seatNumber: carType.seat_number || 4,
                    priority: carType.priority || 999,
                    category: 'other',
                    keywords: [],
                    suitableFor: []
                };

                // 识别车型类别
                const typeName = profile.type.toLowerCase();
                for (const [category, pattern] of Object.entries(vehiclePatterns)) {
                    if (pattern.test(typeName)) {
                        profile.category = category;
                        break;
                    }
                }

                // 根据座位数确定适用范围
                for (const [range, config] of Object.entries(seatRanges)) {
                    if (profile.seatNumber >= config.min && profile.seatNumber <= config.max) {
                        profile.suitableFor.push(range);
                    }
                }

                // 生成关键词
                profile.keywords = [
                    profile.category,
                    typeName.replace(/\s+/g, '_'),
                    `${profile.seatNumber}座`,
                    `${profile.seatNumber}_seater`,
                    profile.seatNumber.toString()
                ];

                // 添加特殊关键词
                if (profile.category === 'luxury') {
                    profile.keywords.push('vip', '豪华', 'premium');
                }
                if (profile.category === 'compact') {
                    profile.keywords.push('经济', 'economy', 'budget');
                }

                return profile;
            });

            // 构建动态映射表
            const dynamicMapping = {
                passengerCount: {},
                keywords: {},
                categories: {},
                seatRanges: {}
            };

            // 为每个乘客数量范围分配最优车型
            for (let passengerCount = 1; passengerCount <= 100; passengerCount++) {
                let bestVehicle = null;
                let bestScore = -1;

                for (const profile of vehicleProfiles) {
                    // 计算适配分数
                    let score = 0;

                    // 座位数适配性 (最重要)
                    if (profile.seatNumber >= passengerCount) {
                        score += 100 - (profile.seatNumber - passengerCount) * 2; // 座位数越接近越好
                    } else {
                        continue; // 座位数不足，跳过
                    }

                    // 优先级加分 (优先级越低数字越小，分数越高)
                    score += (1000 - profile.priority) / 10;

                    // 类别适配性加分
                    if (passengerCount <= 2 && profile.category === 'compact') score += 20;
                    else if (passengerCount <= 5 && profile.category === 'comfort') score += 15;
                    else if (passengerCount <= 8 && (profile.category === 'mpv' || profile.category === 'suv')) score += 15;
                    else if (passengerCount > 8 && (profile.category === 'van' || profile.category === 'bus')) score += 20;

                    if (score > bestScore) {
                        bestScore = score;
                        bestVehicle = profile;
                    }
                }

                if (bestVehicle) {
                    dynamicMapping.passengerCount[passengerCount] = {
                        type: bestVehicle.category,
                        id: bestVehicle.id,
                        name: bestVehicle.type,
                        priority: bestVehicle.priority,
                        seatNumber: bestVehicle.seatNumber,
                        score: bestScore
                    };
                }
            }

            // 构建关键词映射
            vehicleProfiles.forEach(profile => {
                profile.keywords.forEach(keyword => {
                    if (!dynamicMapping.keywords[keyword] ||
                        dynamicMapping.keywords[keyword].priority > profile.priority) {
                        dynamicMapping.keywords[keyword] = {
                            id: profile.id,
                            name: profile.type,
                            priority: profile.priority,
                            category: profile.category
                        };
                    }
                });

                // 类别映射
                if (!dynamicMapping.categories[profile.category] ||
                    dynamicMapping.categories[profile.category].priority > profile.priority) {
                    dynamicMapping.categories[profile.category] = {
                        id: profile.id,
                        name: profile.type,
                        priority: profile.priority
                    };
                }
            });

            return {
                strategy: 'dynamic_vehicle_adaptation',
                mapping: dynamicMapping,
                categories: Object.keys(dynamicMapping.categories),
                vehicleProfiles: vehicleProfiles.map(p => ({
                    id: p.id,
                    name: p.type,
                    category: p.category,
                    seats: p.seatNumber,
                    priority: p.priority
                }))
            };

        } catch (error) {
            this.logger.error('SmartSelection', '创建动态车型映射失败', error);
            return {
                strategy: 'fallback_static_mapping',
                mapping: this.vehicleTypeMapping,
                categories: ['compact', 'comfort', 'mpv', 'van', 'bus'],
                vehicleProfiles: []
            };
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SmartSelectionService;
} else if (typeof window !== 'undefined') {
    window.SmartSelectionService = SmartSelectionService;
}
