/**
 * @file api-sync-manager.js - 动态API数据同步管理器
 * @description 实现与GoMyHire API的实时数据同步，自动更新车型、服务类型、用户数据
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 */

/**
 * @class DynamicApiSyncManager - 动态API数据同步管理器
 * @description 实现与GoMyHire API的实时数据同步，自动更新车型、服务类型、用户数据
 */
class DynamicApiSyncManager {
    constructor() {
        this.syncInterval = 30 * 60 * 1000; // 30分钟同步一次
        this.lastSyncTime = null;
        this.syncStatus = 'idle';
        this.retryCount = 0;
        this.maxRetries = 3;
        
        // API端点配置
        this.apiEndpoints = {
            backendUsers: '/api/backend-users',
            subCategories: '/api/sub-categories', 
            carTypes: '/api/car-types'
        };
        
        // 同步状态监听器
        this.statusListeners = [];
        
        // 启动定时同步
        this.startAutoSync();
    }
    
    /**
     * @function startAutoSync - 启动自动同步机制
     */
    startAutoSync() {
        // 首次同步
        this.syncAllData();
        
        // 定时同步
        this.syncTimer = setInterval(() => {
            this.syncAllData();
        }, this.syncInterval);
        
        console.log('DynamicApiSync', '自动同步机制已启动', {
            interval: this.syncInterval,
            endpoints: Object.keys(this.apiEndpoints)
        });
    }
    
    /**
     * @function syncAllData - 同步所有API数据
     * @returns {Promise<Object>} 同步结果
     */
    async syncAllData() {
        try {
            this.syncStatus = 'syncing';
            this.notifyStatusChange('sync_started');
            
            console.log('DynamicApiSync', '开始API数据同步');
            
            // 并行获取所有数据
            const [backendUsers, subCategories, carTypes] = await Promise.all([
                this.fetchBackendUsers(),
                this.fetchSubCategories(),
                this.fetchCarTypes()
            ]);
            
            // 验证数据完整性
            const validationResult = this.validateSyncedData({
                backendUsers,
                subCategories, 
                carTypes
            });
            
            if (validationResult.valid) {
                // 更新应用状态
                this.updateAppState({
                    backendUsers,
                    subCategories,
                    carTypes
                });
                
                this.lastSyncTime = Date.now();
                this.syncStatus = 'success';
                this.retryCount = 0;
                
                console.log('DynamicApiSync', 'API数据同步成功', {
                    backendUsers: backendUsers.length,
                    subCategories: subCategories.length,
                    carTypes: carTypes.length,
                    timestamp: new Date().toISOString()
                });
                
                this.notifyStatusChange('sync_success', {
                    backendUsers: backendUsers.length,
                    subCategories: subCategories.length,
                    carTypes: carTypes.length
                });
                
                return {
                    success: true,
                    data: { backendUsers, subCategories, carTypes },
                    timestamp: this.lastSyncTime
                };
            } else {
                throw new Error(`数据验证失败: ${validationResult.errors.join(', ')}`);
            }
            
        } catch (error) {
            console.error('DynamicApiSync', 'API数据同步失败', error);
            
            this.syncStatus = 'error';
            this.retryCount++;
            
            this.notifyStatusChange('sync_error', {
                error: error.message,
                retryCount: this.retryCount
            });
            
            // 重试机制
            if (this.retryCount < this.maxRetries) {
                console.log('DynamicApiSync', `将在30秒后进行第${this.retryCount + 1}次重试`);
                setTimeout(() => this.syncAllData(), 30000);
            }
            
            return {
                success: false,
                error: error.message,
                retryCount: this.retryCount
            };
        }
    }
    
    /**
     * @function fetchBackendUsers - 获取后台用户数据
     * @returns {Promise<Array>} 用户数据
     */
    async fetchBackendUsers() {
        try {
            // 检查应用状态中是否已有数据
            const app = window.app;
            if (app && app.appState && app.appState.backendUsers) {
                console.log('DynamicApiSync', '使用应用状态中的后台用户数据');
                return app.appState.backendUsers;
            }
            
            // 模拟API调用 - 实际项目中替换为真实API
            console.log('DynamicApiSync', '模拟获取后台用户数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockUsers = [
                { "id": 1, "name": "Super Admin", "phone": "0162234711", "role": "Super Admin" },
                { "id": 105, "name": "Mei Kwan", "phone": null, "role": "Super Admin" },
                { "id": 22, "name": "Zahidah1", "phone": "0132456789", "role": "Operator" },
                { "id": 106, "name": "admin", "phone": null, "role": "Operator" },
                { "id": 108, "name": "meikwan", "phone": "0123456789", "role": "Operator" },
                { "id": 143, "name": "Kk", "phone": "012", "role": "Operator" },
                { "id": 206, "name": "OperatorRinglee", "phone": "0181122334", "role": "Operator" },
                { "id": 362, "name": "Kcy", "phone": null, "role": "Operator" },
                { "id": 110, "name": "Sub Admin", "phone": "0162234711", "role": "Sub_Admin" },
                { "id": 229, "name": "test", "phone": null, "role": "Sub_Admin" },
                { "id": 338, "name": "jcy1", "phone": null, "role": "Sub_Admin" },
                { "id": 163, "name": "Kok1", "phone": null, "role": "Sub_Operator" }
            ];
            
            return mockUsers;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取后台用户数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchSubCategories - 获取子分类数据
     * @returns {Promise<Array>} 子分类数据
     */
    async fetchSubCategories() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.subCategories) {
                console.log('DynamicApiSync', '使用应用状态中的子分类数据');
                return app.appState.subCategories;
            }
            
            console.log('DynamicApiSync', '模拟获取子分类数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockSubCategories = [
                {
                    "id": 7,
                    "main_category": "Airport",
                    "name": "Pickup",
                    "preset_data": {
                        "order_type": "pickup",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English", "KR - Korean", "TML - Tamil"],
                        "extra_requirement": "Pickup at the right time please!"
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 8,
                    "main_category": "Airport",
                    "name": "Dropoff",
                    "preset_data": {
                        "order_type": "dropoff",
                        "ota": null,
                        "driving_region": "KL - RM - Kuala Lumpur",
                        "languages": ["JP - Japanese", "MY - Malay", "CN - Chinese", "EN - English"],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 9,
                    "main_category": "Chartered",
                    "name": "KL to genting",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": ["EN - English"],
                        "extra_requirement": "asdafdsgghfdfgdjhfygfcvxfgdtgcbncbncghfgfhcvbncvb..."
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                },
                {
                    "id": 43,
                    "main_category": "Chartered",
                    "name": "Charter",
                    "preset_data": {
                        "order_type": "charter",
                        "ota": null,
                        "driving_region": null,
                        "languages": [],
                        "extra_requirement": null
                    },
                    "required_fields": ["Customer Name", "Customer Contact", "Driving Region", "Pickup Address", "Pickup Date", "Pickup Time", "Destination Address", "Passenger Number", "Driver Fee"]
                }
            ];
            
            return mockSubCategories;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取子分类数据失败', error);
            throw error;
        }
    }
    
    /**
     * @function fetchCarTypes - 获取车型数据
     * @returns {Promise<Array>} 车型数据
     */
    async fetchCarTypes() {
        try {
            const app = window.app;
            if (app && app.appState && app.appState.carTypes) {
                console.log('DynamicApiSync', '使用应用状态中的车型数据');
                return app.appState.carTypes;
            }
            
            console.log('DynamicApiSync', '模拟获取车型数据');
            
            // 基于gomyhire api id.md中的真实数据
            const mockCarTypes = [
                { "id": 5, "type": "Compact 5 Seater", "seat_number": 4, "priority": 1 },
                { "id": 6, "type": "Comfort 5 Seater", "seat_number": 4, "priority": 2 },
                { "id": 15, "type": "Mid Size SUV", "seat_number": 7, "priority": 3 },
                { "id": 16, "type": "Standard Size MPV", "seat_number": 6, "priority": 4 },
                { "id": 31, "type": "Luxury Mpv", "seat_number": 6, "priority": 5 },
                { "id": 32, "type": "Alphard/Velfire", "seat_number": 6, "priority": 6 },
                { "id": 20, "type": "10 Seater MPV / Van", "seat_number": 9, "priority": 7 },
                { "id": 30, "type": "12 Seater MPV", "seat_number": 11, "priority": 8 },
                { "id": 23, "type": "14 Seater Van", "seat_number": 12, "priority": 9 },
                { "id": 24, "type": "18 Seater Van", "seat_number": 16, "priority": 10 },
                { "id": 25, "type": "30 Seat Mini Bus", "seat_number": 30, "priority": 11 },
                { "id": 26, "type": "44 Seater Bus", "seat_number": 44, "priority": 12 },
                { "id": 34, "type": "Please Refer Live Chat", "seat_number": 1, "priority": 13 }
            ];
            
            return mockCarTypes;
            
        } catch (error) {
            console.error('DynamicApiSync', '获取车型数据失败', error);
            throw error;
        }
    }

    /**
     * @function validateSyncedData - 验证同步的数据
     * @param {Object} data - 同步的数据
     * @returns {Object} 验证结果
     */
    validateSyncedData(data) {
        const errors = [];

        // 验证后台用户数据
        if (!Array.isArray(data.backendUsers) || data.backendUsers.length === 0) {
            errors.push('后台用户数据无效或为空');
        } else {
            const invalidUsers = data.backendUsers.filter(user => !user.id || !user.name);
            if (invalidUsers.length > 0) {
                errors.push(`发现${invalidUsers.length}个无效用户记录`);
            }
        }

        // 验证子分类数据
        if (!Array.isArray(data.subCategories) || data.subCategories.length === 0) {
            errors.push('子分类数据无效或为空');
        } else {
            const invalidCategories = data.subCategories.filter(cat => !cat.id || !cat.name);
            if (invalidCategories.length > 0) {
                errors.push(`发现${invalidCategories.length}个无效分类记录`);
            }
        }

        // 验证车型数据
        if (!Array.isArray(data.carTypes) || data.carTypes.length === 0) {
            errors.push('车型数据无效或为空');
        } else {
            const invalidCarTypes = data.carTypes.filter(car => !car.id || !car.type);
            if (invalidCarTypes.length > 0) {
                errors.push(`发现${invalidCarTypes.length}个无效车型记录`);
            }
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * @function updateAppState - 更新应用状态
     * @param {Object} data - 新数据
     */
    updateAppState(data) {
        try {
            const app = window.app;
            if (app && app.appState) {
                // 更新应用状态
                app.appState.backendUsers = data.backendUsers;
                app.appState.subCategories = data.subCategories;
                app.appState.carTypes = data.carTypes;

                // 触发智能选择服务更新映射表
                if (window.smartSelection) {
                    window.smartSelection.updateMappingFromAppState();
                }

                console.log('DynamicApiSync', '应用状态已更新', {
                    backendUsersCount: data.backendUsers.length,
                    subCategoriesCount: data.subCategories.length,
                    carTypesCount: data.carTypes.length
                });
            }
        } catch (error) {
            console.error('DynamicApiSync', '更新应用状态失败', error);
        }
    }

    /**
     * @function addStatusListener - 添加状态监听器
     * @param {Function} listener - 监听器函数
     */
    addStatusListener(listener) {
        this.statusListeners.push(listener);
    }

    /**
     * @function notifyStatusChange - 通知状态变更
     * @param {string} status - 状态
     * @param {Object} data - 附加数据
     */
    notifyStatusChange(status, data = {}) {
        this.statusListeners.forEach(listener => {
            try {
                listener(status, data);
            } catch (error) {
                console.error('DynamicApiSync', '状态监听器执行失败', error);
            }
        });
    }

    /**
     * @function forceSync - 强制立即同步
     * @returns {Promise<Object>} 同步结果
     */
    async forceSync() {
        console.log('DynamicApiSync', '强制执行数据同步');
        return await this.syncAllData();
    }

    /**
     * @function getSyncStatus - 获取同步状态
     * @returns {Object} 同步状态信息
     */
    getSyncStatus() {
        return {
            status: this.syncStatus,
            lastSyncTime: this.lastSyncTime,
            retryCount: this.retryCount,
            nextSyncTime: this.lastSyncTime ? this.lastSyncTime + this.syncInterval : null
        };
    }

    /**
     * @function stopAutoSync - 停止自动同步
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('DynamicApiSync', '自动同步已停止');
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicApiSyncManager;
} else if (typeof window !== 'undefined') {
    window.DynamicApiSyncManager = DynamicApiSyncManager;
}
