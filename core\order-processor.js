/**
 * @file order-processor.js - 订单处理模块
 * @description 订单解析、智能选择集成、结果显示、数据收集
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 */

/**
 * @class OrderProcessor - 订单处理类
 * @description 管理订单的解析、处理、显示和创建流程
 */
class OrderProcessor {
    /**
     * @function constructor - 构造函数
     * @description 初始化订单处理器
     * @param {AppState} appState - 应用状态实例
     * @param {OrderParser} orderParser - 订单解析器实例
     * @param {SmartSelectionService} smartSelection - 智能选择服务实例
     */
    constructor(appState, orderParser, smartSelection) {
        this.appState = appState;
        this.orderParser = orderParser;
        this.smartSelection = smartSelection;
        this.currentOrders = [];
        this.processingState = {
            isProcessing: false,
            currentStep: '',
            progress: 0
        };

        // 初始化渲染器
        this.renderer = new OrderRenderer(appState);
    }

    /**
     * @function handleProcessOrder - 处理订单
     * @description 处理用户输入的订单文本
     */
    async handleProcessOrder() {
        const textInput = document.getElementById('orderText').value.trim();
        const otaType = document.getElementById('otaSelect').value;

        if (!textInput) {
            window.app.uiManager.showError('请输入订单文本');
            return;
        }

        logger.info('OrderProcessor', '开始处理订单', {
            textLength: textInput.length,
            otaType: otaType
        });

        try {
            this.processingState.isProcessing = true;
            window.app.uiManager.showLoading('正在处理订单...');

            const result = await this.orderParser.parseOrders(textInput, otaType);

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.currentOrders = result.orders;
                this.renderer.displayOrderResults(result);
                logger.success('OrderProcessor', '订单处理完成', {
                    orderCount: result.orders.length
                });
            } else {
                throw new Error(result.error || '订单处理失败');
            }

        } catch (error) {
            logger.error('OrderProcessor', '订单处理失败', { error: error.message });
            window.app.uiManager.showError('订单处理失败: ' + error.message);
        } finally {
            this.processingState.isProcessing = false;
            window.app.uiManager.hideLoading();
        }
    }

    /**
     * @function handleCreateOrders - 创建订单（集成错误恢复机制）
     * @description 批量创建订单，包含完整的错误恢复机制
     */
    async handleCreateOrders() {
        try {
            logger.info('OrderProcessor', '开始创建订单流程');
            
            // 1. 数据一致性预检查
            const isDataValid = await window.app.dataConsistencyManager.validateUserData();
            if (!isDataValid) {
                logger.warn('OrderProcessor', '数据一致性检查失败，正在刷新数据...');
                await window.app.dataConsistencyManager.forceDataRefresh();
                
                // 更新UI选择器
                window.app.uiManager.updateUISelectors();
                
                // 提示用户
                window.app.uiManager.showError('系统数据已更新，请重新检查选择项');
                return;
            }

            // 显示加载状态
            window.app.uiManager.showLoading('正在创建订单...');
            
            // 收集订单数据
            const orders = await this.collectAllOrders();
            
            if (!orders || orders.length === 0) {
                window.app.uiManager.hideLoading();
                window.app.uiManager.showError('没有找到要创建的订单');
                return;
            }

            logger.info('OrderProcessor', `准备创建 ${orders.length} 个订单`);

            // 创建结果存储
            const results = [];
            
            // 逐个处理订单（支持错误恢复）
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                logger.debug('OrderProcessor', `正在处理第 ${i + 1} 个订单`, order);

                try {
                    // 尝试创建订单
                    const result = await window.app.apiService.createOrder(order);
                    results.push({ 
                        success: true, 
                        result, 
                        orderData: order,
                        index: i 
                    });
                    logger.success('OrderProcessor', `第 ${i + 1} 个订单创建成功`, result);

                } catch (error) {
                    logger.error('OrderProcessor', `第 ${i + 1} 个订单创建失败`, error);
                    
                    // 错误分析和恢复尝试
                    const errorAnalysis = window.app.errorRecoveryManager.analyzeError(error);
                    
                    if (errorAnalysis.recoverable) {
                        logger.info('OrderProcessor', '尝试错误恢复', errorAnalysis);
                        
                        const recoveryResult = await window.app.errorRecoveryManager.attemptRecovery(
                            errorAnalysis, 
                            order
                        );
                        
                        if (recoveryResult.success) {
                            results.push({ 
                                success: true, 
                                result: recoveryResult.result, 
                                orderData: recoveryResult.orderData,
                                index: i,
                                recovered: true 
                            });
                            logger.success('OrderProcessor', `第 ${i + 1} 个订单错误恢复成功`);
                        } else {
                            results.push({ 
                                success: false, 
                                error: this.formatErrorMessage(error, recoveryResult),
                                orderData: order,
                                index: i,
                                errorAnalysis 
                            });
                        }
                    } else {
                        // 不可恢复的错误
                        results.push({ 
                            success: false, 
                            error: this.formatErrorMessage(error),
                            orderData: order,
                            index: i,
                            errorAnalysis 
                        });
                    }
                }
            }

            // 隐藏加载状态
            window.app.uiManager.hideLoading();

            // 显示创建结果
            this.renderer.displayCreateResults(results);

            // 如果有成功的订单，添加到状态
            const successfulOrders = results.filter(r => r.success);
            if (successfulOrders.length > 0) {
                this.appState.processedOrders.push(...successfulOrders.map(r => r.orderData));
                
                // 显示成功通知
                if (window.notificationManager) {
                    window.notificationManager.success(
                        '订单创建成功',
                        `成功创建 ${successfulOrders.length}/${results.length} 个订单`
                    );
                }
            }

        } catch (error) {
            logger.error('OrderProcessor', '创建订单流程失败', error);
            window.app.uiManager.hideLoading();
            window.app.uiManager.showError('创建订单过程中发生错误: ' + error.message);
        }
    }

    /**
     * @function collectAllOrders - 收集所有订单数据
     * @description 从UI中收集所有要创建的订单数据
     * @returns {Promise<Array>} 订单数据数组
     */
    async collectAllOrders() {
        const orders = [];

        // 收集已处理的订单
        if (this.appState.processedOrders && this.appState.processedOrders.length > 0) {
            for (const order of this.appState.processedOrders) {
                const processedOrder = await this.processOrderForAPI(order);
                if (processedOrder) {
                    orders.push(processedOrder);
                }
            }
        }

        // 收集手动编辑的订单
        const manualOrders = await this.collectManualOrders();
        orders.push(...manualOrders);

        return orders;
    }

    /**
     * @function collectManualOrders - 收集手动编辑的订单
     * @description 从手动编辑表单中收集订单数据
     * @returns {Promise<Array>} 手动订单数据数组
     */
    async collectManualOrders() {
        const orders = [];
        const forms = document.querySelectorAll('.order-edit-form');

        for (const form of forms) {
            try {
                const orderData = await this.extractOrderDataFromForm(form);
                if (orderData && this.validateOrderData(orderData)) {
                    orders.push(orderData);
                }
            } catch (error) {
                logger.warn('OrderProcessor', '手动订单数据提取失败', error);
            }
        }

        return orders;
    }

    /**
     * @function extractOrderDataFromForm - 从表单提取订单数据
     * @description 从订单编辑表单中提取数据
     * @param {HTMLElement} form - 表单元素
     * @returns {Promise<object>} 订单数据对象
     */
    async extractOrderDataFromForm(form) {
        const formData = new FormData(form);
        const orderData = {};

        // 提取基本字段
        const fieldMapping = {
            'customerName': 'customer_name',
            'customerContact': 'customer_contact',
            'flightInfo': 'flight_number',
            'pickup': 'pickup_location',
            'destination': 'drop_location',
            'date': 'service_date',
            'time': 'service_time',
            'passengerNumber': 'passenger_count',
            'extraRequirement': 'extra_requirement'
        };

        for (const [formField, apiField] of Object.entries(fieldMapping)) {
            const value = formData.get(formField);
            if (value) {
                orderData[apiField] = value;
            }
        }

        // 处理选择器字段
        const backendUserId = formData.get('backendUser');
        const subCategoryId = formData.get('subCategory');
        const carTypeId = formData.get('carType');

        if (backendUserId) orderData.backend_user_id = parseInt(backendUserId);
        if (subCategoryId) orderData.sub_category_id = parseInt(subCategoryId);
        if (carTypeId) orderData.car_type_id = parseInt(carTypeId);

        // 生成OTA参考号
        orderData.ota_reference_number = await this.generateOTAReference('manual', orderData);

        // 确保必需字段
        await this.ensureRequiredApiFields(orderData);

        return orderData;
    }

    /**
     * @function processOrderForAPI - 处理订单数据以适配API
     * @description 将解析的订单数据转换为API格式
     * @param {object} order - 原始订单数据
     * @returns {Promise<object>} API格式的订单数据
     */
    async processOrderForAPI(order) {
        try {
            // 复制订单数据
            const apiOrder = { ...order };

            // 确保日期格式正确（DD-MM-YYYY）
            if (apiOrder.service_date) {
                apiOrder.service_date = this.formatDateForAPI(apiOrder.service_date);
            }

            // 确保乘客数量为数字
            if (apiOrder.passenger_count) {
                apiOrder.passenger_count = parseInt(apiOrder.passenger_count) || 1;
            }

            // 生成OTA参考号（如果没有）
            if (!apiOrder.ota_reference_number) {
                apiOrder.ota_reference_number = await this.generateOTAReference('auto', apiOrder);
            }

            // 应用智能选择
            if (this.smartSelection) {
                const smartResult = await this.applySmartSelection(apiOrder);
                Object.assign(apiOrder, smartResult);
            }

            // 确保所有必需字段
            await this.ensureRequiredApiFields(apiOrder);

            return apiOrder;

        } catch (error) {
            logger.error('OrderProcessor', '订单API格式转换失败', error);
            return null;
        }
    }

    /**
     * @function applySmartSelection - 应用智能选择
     * @description 使用智能选择服务自动选择车型、用户等
     * @param {object} orderData - 订单数据
     * @returns {Promise<object>} 智能选择结果
     */
    async applySmartSelection(orderData) {
        const selections = {};

        try {
            // 智能选择车型
            if (!orderData.car_type_id && this.smartSelection) {
                const vehicleResult = this.smartSelection.selectVehicleType(orderData);
                if (vehicleResult && vehicleResult.carTypeId) {
                    selections.car_type_id = vehicleResult.carTypeId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.vehicle = vehicleResult;
                }
            }

            // 智能选择服务类型
            if (!orderData.sub_category_id && this.smartSelection) {
                const serviceResult = this.smartSelection.selectServiceType(orderData);
                if (serviceResult && serviceResult.subCategoryId) {
                    selections.sub_category_id = serviceResult.subCategoryId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.service = serviceResult;
                }
            }

            // 智能选择后台用户
            if (!orderData.backend_user_id && this.smartSelection) {
                const userResult = this.smartSelection.selectBackendUser(orderData);
                if (userResult && userResult.backendUserId) {
                    selections.backend_user_id = userResult.backendUserId;
                    selections._smartSelection = selections._smartSelection || {};
                    selections._smartSelection.user = userResult;
                }
            }

            return selections;

        } catch (error) {
            logger.warn('OrderProcessor', '智能选择应用失败', error);
            return {};
        }
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @description 根据OTA类型生成不同格式的参考号
     * @param {string} otaType - OTA类型
     * @param {object} orderData - 订单数据
     * @returns {Promise<string>} OTA参考号
     */
    async generateOTAReference(otaType, orderData) {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');

        switch (otaType) {
            case 'chong-dealer':
                return this.generateChongDealerReference(orderData, dateStr, timeStr);
                
            case 'fallback':
                return await this.generateFallbackReference(orderData, dateStr, timeStr);
                
            default:
                // 默认格式
                const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
                return `OTA${dateStr}${timeStr}${randomNum}`;
        }
    }

    /**
     * @function generateChongDealerReference - 生成Chong Dealer参考号
     * @description 为Chong Dealer订单生成专用格式的参考号
     * @param {object} orderData - 订单数据
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @returns {string} Chong Dealer参考号
     */
    generateChongDealerReference(orderData, dateStr, timeStr) {
        // 提取客人姓名首字母
        const customerInitial = orderData.customer_name ? 
            orderData.customer_name.charAt(0).toUpperCase() : 'X';
        
        // 提取航班号（如果有）
        const flightCode = orderData.flight_number ? 
            orderData.flight_number.replace(/[^A-Z0-9]/g, '').slice(0, 6) : '';
        
        // 随机2位数
        const randomNum = Math.floor(Math.random() * 100).toString().padStart(2, '0');
        
        return `CD${dateStr}${timeStr}${customerInitial}${flightCode}${randomNum}`;
    }

    /**
     * @function generateFallbackReference - 生成Fallback参考号
     * @description 为通用模板生成参考号，尝试提取原始订单号
     * @param {object} orderData - 订单数据
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @returns {Promise<string>} Fallback参考号
     */
    async generateFallbackReference(orderData, dateStr, timeStr) {
        try {
            // 尝试从订单数据中提取原始订单号
            const originalOrderNumber = await this.extractOriginalOrderNumber(orderData);
            
            if (originalOrderNumber) {
                return `FB${dateStr}${originalOrderNumber}`;
            } else {
                // 如果无法提取，使用随机生成
                const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                return `FB${dateStr}${timeStr}${randomNum}`;
            }
            
        } catch (error) {
            logger.warn('OrderProcessor', 'Fallback参考号生成失败，使用默认格式', error);
            const randomNum = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            return `FB${dateStr}${timeStr}${randomNum}`;
        }
    }

    /**
     * @function extractOriginalOrderNumber - 提取原始订单号
     * @description 从订单数据中智能提取原始平台的订单号
     * @param {object} orderData - 订单数据
     * @returns {Promise<string|null>} 原始订单号或null
     */
    async extractOriginalOrderNumber(orderData) {
        try {
            // 构建用于分析的文本
            const analysisText = [
                orderData.customer_name,
                orderData.customer_contact,
                orderData.pickup_location,
                orderData.drop_location,
                orderData.extra_requirement,
                orderData._originalText
            ].filter(Boolean).join(' ');

            if (!analysisText) {
                return null;
            }

            // 使用LLM智能提取订单号
            if (window.app.llmService) {
                const extractedNumber = await window.app.llmService.extractOrderNumber(analysisText);
                if (extractedNumber) {
                    return extractedNumber;
                }
            }

            // 降级到正则表达式提取
            return this.extractOrderNumberWithRegex(analysisText);

        } catch (error) {
            logger.warn('OrderProcessor', '原始订单号提取失败', error);
            return null;
        }
    }

    /**
     * @function extractOrderNumberWithRegex - 使用正则表达式提取订单号
     * @description 使用正则表达式模式提取可能的订单号
     * @param {string} text - 要分析的文本
     * @returns {string|null} 提取的订单号或null
     */
    extractOrderNumberWithRegex(text) {
        // 定义订单号模式（按优先级排序）
        const patterns = [
            /\b\d{8,16}\b/g,                    // 8-16位纯数字
            /\b[A-Z]{2,4}\d{4,8}\b/g,          // 2-4个字母 + 4-8位数字
            /\b\d{4}[A-Z]{2}\d{4,8}\b/g,       // 特殊格式：数字+字母+数字
            /\b[A-Z0-9]{8,12}\b/g              // 8-12位字母数字混合
        ];

        for (const pattern of patterns) {
            const matches = text.match(pattern);
            if (matches && matches.length > 0) {
                // 返回第一个匹配的结果
                return matches[0];
            }
        }

        return null;
    }

    /**
     * @function ensureRequiredApiFields - 确保必需的API字段
     * @description 确保订单数据包含所有必需的API字段
     * @param {object} orderData - 订单数据
     * @returns {Promise<void>}
     */
    async ensureRequiredApiFields(orderData) {
        // 确保乘客数量
        if (!orderData.passenger_count || orderData.passenger_count < 1) {
            orderData.passenger_count = 1;
        }

        // 确保车型ID（使用默认车型）
        if (!orderData.car_type_id && this.appState.carTypes.length > 0) {
            // 使用Comfort 5 Seater (ID: 1)作为默认车型
            const defaultCarType = this.appState.carTypes.find(ct => ct.id === 1) ||
                                   this.appState.carTypes[0];
            orderData.car_type_id = defaultCarType.id;
        }

        // 确保服务类型ID
        if (!orderData.sub_category_id && this.appState.subCategories.length > 0) {
            orderData.sub_category_id = this.appState.subCategories[0].id;
        }

        // 确保后台用户ID
        if (!orderData.backend_user_id && this.appState.backendUsers.length > 0) {
            orderData.backend_user_id = this.appState.backendUsers[0].id;
        }

        // 确保日期格式
        if (orderData.service_date) {
            orderData.service_date = this.formatDateForAPI(orderData.service_date);
        }
    }

    /**
     * @function formatDateForAPI - 格式化日期为API格式
     * @description 将日期转换为DD-MM-YYYY格式
     * @param {string} dateStr - 原始日期字符串
     * @returns {string} 格式化的日期字符串
     */
    formatDateForAPI(dateStr) {
        try {
            const date = new Date(dateStr);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();
            return `${day}-${month}-${year}`;
        } catch (error) {
            logger.warn('OrderProcessor', '日期格式化失败', { dateStr, error });
            return dateStr;
        }
    }

    /**
     * @function validateOrderData - 验证订单数据
     * @description 验证订单数据的完整性和有效性
     * @param {object} orderData - 订单数据
     * @returns {boolean} 验证结果
     */
    validateOrderData(orderData) {
        const requiredFields = ['customer_name', 'pickup_location', 'drop_location'];

        for (const field of requiredFields) {
            if (!orderData[field] || orderData[field].trim() === '') {
                logger.warn('OrderProcessor', `订单数据验证失败：缺少${field}`, orderData);
                return false;
            }
        }

        return true;
    }









    /**
     * @function formatErrorMessage - 格式化错误消息
     * @description 格式化错误消息为用户友好的格式
     * @param {Error} error - 错误对象
     * @param {object} recoveryResult - 恢复结果（可选）
     * @returns {string} 格式化的错误消息
     */
    formatErrorMessage(error, recoveryResult = null) {
        let message = error.message || '未知错误';

        // 简化常见错误消息
        if (message.includes('validation')) {
            message = '数据验证失败';
        } else if (message.includes('unauthorized')) {
            message = '认证失败，请重新登录';
        } else if (message.includes('not found')) {
            message = '数据不存在，请刷新系统数据';
        }

        // 添加恢复失败信息
        if (recoveryResult && !recoveryResult.success) {
            message += ` (自动恢复失败: ${recoveryResult.reason})`;
        }

        return message;
    }

    /**
     * @function handleEditResults - 处理编辑结果
     * @description 启用订单结果的编辑模式
     */
    handleEditResults() {
        logger.info('OrderProcessor', '启用编辑模式');

        // 显示手动编辑区域
        window.app.uiManager.toggleSection('manualEditSection', true);

        // 为每个订单创建编辑表单
        this.renderer.createOrderEditForms();

        window.app.uiManager.showInfo('编辑模式已启用，您可以修改订单信息');
    }







    /**
     * @function handleAddOrder - 处理添加订单
     * @description 添加新的空白订单编辑表单
     */
    handleAddOrder() {
        this.renderer.addNewOrderForm();
    }

    /**
     * @function handleRefreshResults - 处理刷新结果
     * @description 重新处理当前的订单文本
     */
    async handleRefreshResults() {
        const textInput = document.getElementById('orderText').value.trim();
        if (!textInput) {
            window.app.uiManager.showError('没有可重新处理的订单文本');
            return;
        }

        logger.info('OrderProcessor', '重新处理订单');
        await this.handleProcessOrder();
    }

    /**
     * @function handleReAnalyze - 处理重新分析
     * @description 使用不同的LLM重新分析订单
     */
    async handleReAnalyze() {
        const textInput = document.getElementById('orderText').value.trim();
        if (!textInput) {
            window.app.uiManager.showError('没有可重新分析的订单文本');
            return;
        }

        logger.info('OrderProcessor', '重新分析订单');

        try {
            window.app.uiManager.showLoading('正在重新分析...');

            // 强制使用备用LLM重新分析
            const result = await this.orderParser.parseOrders(textInput, 'fallback', true);

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.currentOrders = result.orders;
                this.renderer.displayOrderResults(result);
                window.app.uiManager.showSuccess('重新分析完成');
            } else {
                throw new Error(result.error || '重新分析失败');
            }

        } catch (error) {
            logger.error('OrderProcessor', '重新分析失败', error);
            window.app.uiManager.showError('重新分析失败: ' + error.message);
        } finally {
            window.app.uiManager.hideLoading();
        }
    }
}

// 导出到全局作用域（向后兼容）
window.OrderProcessor = OrderProcessor;

logger.info('模块', 'OrderProcessor模块加载完成', { version: 'v4.0.1' });
