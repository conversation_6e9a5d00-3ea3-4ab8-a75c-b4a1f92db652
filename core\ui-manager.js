/**
 * @file ui-manager.js - UI界面管理模块
 * @description UI初始化、选择器管理、模态框控制、状态指示器
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 */

/**
 * @class UIManager - UI界面管理类
 * @description 管理所有UI相关的操作，包括初始化、状态更新、模态框控制
 */
class UIManager {
    /**
     * @function constructor - 构造函数
     * @description 初始化UI管理器
     * @param {AppState} appState - 应用状态实例
     */
    constructor(appState) {
        this.appState = appState;
        this.loadingCount = 0; // 加载状态计数器
        this.currentModal = null; // 当前显示的模态框
    }

    /**
     * @function initializeUI - 初始化UI组件
     * @description 初始化所有UI组件和状态
     */
    initializeUI() {
        logger.info('UIManager', '开始初始化UI组件');

        // 显示登录模态框（如果未登录）
        if (!this.appState.token) {
            this.showLoginModal();
        }

        // 初始化状态指示器
        this.updateConnectionStatus();

        // 初始化文件上传区域
        this.initializeFileUpload();

        // 初始化地址搜索组件
        this.initializeAddressSearchComponents();

        logger.debug('UIManager', 'UI组件初始化完成');
    }

    /**
     * @function initializeFileUpload - 初始化文件上传区域
     * @description 设置文件上传区域的样式和提示
     */
    initializeFileUpload() {
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            // 设置拖拽区域样式
            uploadArea.style.border = '2px dashed #ccc';
            uploadArea.style.borderRadius = '10px';
            uploadArea.style.textAlign = 'center';
            uploadArea.style.padding = '20px';
            uploadArea.style.margin = '10px 0';
            uploadArea.style.cursor = 'pointer';
            
            // 添加提示文本
            if (!uploadArea.querySelector('.upload-hint')) {
                const hint = document.createElement('div');
                hint.className = 'upload-hint';
                hint.innerHTML = '📁 点击选择文件或拖拽图片到此处';
                hint.style.color = '#666';
                hint.style.fontSize = '14px';
                uploadArea.appendChild(hint);
            }
        }
    }

    /**
     * @function initializeAddressSearchComponents - 初始化地址搜索组件
     * @description 为地址输入框添加搜索功能组件
     */
    initializeAddressSearchComponents() {
        const addressInputs = document.querySelectorAll('input[data-address-search]');
        addressInputs.forEach(input => {
            this.setupAddressSearchForInput(input);
        });
    }

    /**
     * @function setupAddressSearchForInput - 为输入框设置地址搜索
     * @description 为特定输入框添加地址搜索功能
     * @param {HTMLElement} input - 输入框元素
     */
    setupAddressSearchForInput(input) {
        // 创建搜索建议容器
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'address-suggestions';
        suggestionsContainer.style.display = 'none';
        
        // 插入到输入框后面
        input.parentNode.insertBefore(suggestionsContainer, input.nextSibling);
        
        // 存储引用
        input.suggestionsContainer = suggestionsContainer;
    }

    /**
     * @function showLoginModal - 显示登录模态框
     * @description 显示用户登录界面
     */
    showLoginModal() {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'block';
            this.currentModal = modal;
            logger.debug('UIManager', '显示登录模态框');
        }
    }

    /**
     * @function hideLoginModal - 隐藏登录模态框
     * @description 隐藏用户登录界面
     */
    hideLoginModal() {
        const modal = document.getElementById('loginModal');
        if (modal) {
            modal.style.display = 'none';
            this.currentModal = null;
            logger.debug('UIManager', '隐藏登录模态框');
        }
    }

    /**
     * @function showLoading - 显示加载状态
     * @description 显示加载指示器和消息
     * @param {string} message - 加载消息
     */
    showLoading(message = '加载中...') {
        this.loadingCount++;
        
        const loadingDiv = document.getElementById('loadingIndicator') || this.createLoadingIndicator();
        const messageDiv = loadingDiv.querySelector('.loading-message');
        
        if (messageDiv) {
            messageDiv.textContent = message;
        }
        
        loadingDiv.style.display = 'flex';
        logger.debug('UIManager', '显示加载状态', { message, count: this.loadingCount });
    }

    /**
     * @function hideLoading - 隐藏加载状态
     * @description 隐藏加载指示器
     */
    hideLoading() {
        this.loadingCount = Math.max(0, this.loadingCount - 1);
        
        if (this.loadingCount === 0) {
            const loadingDiv = document.getElementById('loadingIndicator');
            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
            logger.debug('UIManager', '隐藏加载状态');
        }
    }

    /**
     * @function createLoadingIndicator - 创建加载指示器
     * @description 动态创建加载指示器元素
     * @returns {HTMLElement} 加载指示器元素
     */
    createLoadingIndicator() {
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'loadingIndicator';
        loadingDiv.className = 'loading-overlay';
        loadingDiv.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <div class="loading-message">加载中...</div>
            </div>
        `;
        
        // 添加样式
        loadingDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        `;
        
        document.body.appendChild(loadingDiv);
        return loadingDiv;
    }

    /**
     * @function updateConnectionStatus - 更新连接状态
     * @description 更新系统连接状态指示器
     */
    updateConnectionStatus() {
        const statusIndicator = document.getElementById('connectionStatus');
        if (statusIndicator) {
            if (this.appState.token) {
                statusIndicator.textContent = '🟢 已连接';
                statusIndicator.className = 'status-connected';
            } else {
                statusIndicator.textContent = '🔴 未连接';
                statusIndicator.className = 'status-disconnected';
            }
        }
    }

    /**
     * @function updateLLMStatusUI - 更新LLM状态UI
     * @description 更新LLM服务连接状态显示
     * @param {object} geminiStatus - Gemini状态对象
     */
    updateLLMStatusUI(geminiStatus) {
        const geminiIndicator = document.getElementById('geminiStatusIndicator');
        if (geminiIndicator) {
            if (geminiStatus.connected) {
                geminiIndicator.textContent = '🟢 Gemini';
                geminiIndicator.className = 'llm-status connected';
                geminiIndicator.title = 'Gemini AI 连接正常';
            } else {
                geminiIndicator.textContent = '🔴 Gemini';
                geminiIndicator.className = 'llm-status disconnected';
                geminiIndicator.title = `Gemini AI 连接失败: ${geminiStatus.error || '未知错误'}`;
            }
        }

        // 更新DeepSeek状态（如果有）
        const deepseekIndicator = document.getElementById('deepseekStatusIndicator');
        if (deepseekIndicator) {
            // 默认显示为可用状态
            deepseekIndicator.textContent = '🟢 DeepSeek';
            deepseekIndicator.className = 'llm-status connected';
            deepseekIndicator.title = 'DeepSeek AI 服务可用';
        }
    }

    /**
     * @function updateUISelectors - 更新UI选择器
     * @description 更新所有下拉选择器的选项
     */
    updateUISelectors() {
        logger.info('UIManager', '更新UI选择器');

        // 更新后台用户选择器
        this.updateBackendUserSelector();
        
        // 更新服务类型选择器
        this.updateSubCategorySelector();
        
        // 更新车型选择器
        this.updateCarTypeSelector();

        logger.debug('UIManager', 'UI选择器更新完成');
    }

    /**
     * @function updateBackendUserSelector - 更新后台用户选择器
     * @description 更新后台用户下拉选择器的选项
     */
    updateBackendUserSelector() {
        const selector = document.getElementById('backendUserSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择后台用户...</option>';

        // 添加用户选项
        if (this.appState.backendUsers && this.appState.backendUsers.length > 0) {
            this.appState.backendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.phone})`;
                selector.appendChild(option);
            });
            
            logger.debug('UIManager', '后台用户选择器更新完成', { 
                count: this.appState.backendUsers.length 
            });
        } else {
            logger.warn('UIManager', '没有可用的后台用户数据');
        }
    }

    /**
     * @function updateSubCategorySelector - 更新服务类型选择器
     * @description 更新服务类型下拉选择器的选项
     */
    updateSubCategorySelector() {
        const selector = document.getElementById('subCategorySelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择服务类型...</option>';

        // 添加服务类型选项
        if (this.appState.subCategories && this.appState.subCategories.length > 0) {
            this.appState.subCategories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                selector.appendChild(option);
            });
            
            logger.debug('UIManager', '服务类型选择器更新完成', { 
                count: this.appState.subCategories.length 
            });
        } else {
            logger.warn('UIManager', '没有可用的服务类型数据');
        }
    }

    /**
     * @function updateCarTypeSelector - 更新车型选择器
     * @description 更新车型下拉选择器的选项
     */
    updateCarTypeSelector() {
        const selector = document.getElementById('carTypeSelect');
        if (!selector) return;

        // 清空现有选项
        selector.innerHTML = '<option value="">选择车型...</option>';

        // 添加车型选项
        if (this.appState.carTypes && this.appState.carTypes.length > 0) {
            this.appState.carTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type} (${carType.seat_number}座)`;
                selector.appendChild(option);
            });
            
            logger.debug('UIManager', '车型选择器更新完成', { 
                count: this.appState.carTypes.length 
            });
        } else {
            logger.warn('UIManager', '没有可用的车型数据');
        }
    }

    /**
     * @function clearUIDisplays - 清空UI显示内容
     * @description 清空所有UI显示区域的内容
     */
    clearUIDisplays() {
        // 清空选择器
        this.clearAllSelectors();

        // 隐藏结果区域
        const sections = ['resultPreview', 'orderStatus', 'manualEditSection'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.classList.add('hidden');
        });

        // 清空表单
        const forms = document.querySelectorAll('.order-edit-form');
        forms.forEach(form => form.remove());

        // 清空结果显示
        const resultsDiv = document.getElementById('orderResults');
        if (resultsDiv) resultsDiv.innerHTML = '';

        logger.debug('UIManager', 'UI显示内容清理完成');
    }

    /**
     * @function clearAllSelectors - 清空所有选择器
     * @description 重置所有下拉选择器为默认状态
     */
    clearAllSelectors() {
        const selectors = ['backendUserSelect', 'subCategorySelect', 'carTypeSelect'];
        selectors.forEach(id => {
            const selector = document.getElementById(id);
            if (selector) {
                selector.innerHTML = '<option value="">选择...</option>';
            }
        });
    }

    /**
     * @function showError - 显示错误消息
     * @description 显示错误提示消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        if (window.notificationManager) {
            window.notificationManager.error('错误', message);
        } else {
            alert('错误: ' + message);
        }
        logger.error('UIManager', '显示错误消息', { message });
    }

    /**
     * @function showSuccess - 显示成功消息
     * @description 显示成功提示消息
     * @param {string} message - 成功消息
     */
    showSuccess(message) {
        if (window.notificationManager) {
            window.notificationManager.success('成功', message);
        } else {
            alert('成功: ' + message);
        }
        logger.info('UIManager', '显示成功消息', { message });
    }

    /**
     * @function showInfo - 显示信息消息
     * @description 显示信息提示消息
     * @param {string} message - 信息消息
     */
    showInfo(message) {
        if (window.notificationManager) {
            window.notificationManager.info('信息', message);
        } else {
            alert('信息: ' + message);
        }
        logger.info('UIManager', '显示信息消息', { message });
    }

    /**
     * @function toggleSection - 切换区域显示状态
     * @description 显示或隐藏指定的UI区域
     * @param {string} sectionId - 区域ID
     * @param {boolean} show - 是否显示
     */
    toggleSection(sectionId, show) {
        const section = document.getElementById(sectionId);
        if (section) {
            if (show) {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        }
    }

    /**
     * @function scrollToSection - 滚动到指定区域
     * @description 平滑滚动到指定的UI区域
     * @param {string} sectionId - 区域ID
     */
    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            section.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    /**
     * @function updateProgress - 更新进度显示
     * @description 更新操作进度显示
     * @param {number} current - 当前进度
     * @param {number} total - 总数
     * @param {string} message - 进度消息
     */
    updateProgress(current, total, message = '') {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            const percentage = Math.round((current / total) * 100);
            progressBar.style.width = percentage + '%';
            progressBar.setAttribute('aria-valuenow', percentage);
        }

        if (progressText) {
            progressText.textContent = message || `${current}/${total} (${Math.round((current / total) * 100)}%)`;
        }
    }

    /**
     * @function resetProgress - 重置进度显示
     * @description 重置进度条和文本
     */
    resetProgress() {
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');

        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        if (progressText) {
            progressText.textContent = '';
        }
    }
}

// 导出到全局作用域（向后兼容）
window.UIManager = UIManager;

logger.info('模块', 'UIManager模块加载完成', { version: 'v4.0.1' });
