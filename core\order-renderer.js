/**
 * @file order-renderer.js - 订单渲染模块
 * @description 订单结果显示、表单生成、UI渲染相关功能
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 */

/**
 * @class OrderRenderer - 订单渲染类
 * @description 管理订单相关的UI渲染和表单生成
 */
class OrderRenderer {
    /**
     * @function constructor - 构造函数
     * @description 初始化订单渲染器
     * @param {AppState} appState - 应用状态实例
     */
    constructor(appState) {
        this.appState = appState;
    }

    /**
     * @function displayOrderResults - 显示订单处理结果
     * @description 在UI中显示订单解析和处理的结果
     * @param {object} result - 处理结果
     */
    displayOrderResults(result) {
        const resultsDiv = document.getElementById('orderResults');
        if (!resultsDiv) return;

        let html = `
            <div class="results-header">
                <h3>订单处理结果</h3>
                <div class="results-summary">
                    <span class="order-count">识别到 ${result.orders.length} 个订单</span>
                    <span class="ota-type">OTA类型: ${result.otaType || '未知'}</span>
                </div>
            </div>
        `;

        if (result.orders.length > 0) {
            html += '<div class="orders-list">';
            result.orders.forEach((order, index) => {
                html += this.renderOrderCard(order, index);
            });
            html += '</div>';

            // 显示操作按钮
            html += `
                <div class="results-actions">
                    <button id="editBtn" class="btn btn-secondary">编辑订单</button>
                    <button id="createOrderBtn" class="btn btn-primary">创建订单</button>
                </div>
            `;
        } else {
            html += '<div class="no-orders">未识别到有效订单</div>';
        }

        resultsDiv.innerHTML = html;

        // 显示结果区域
        window.app.uiManager.toggleSection('resultPreview', true);
        window.app.uiManager.scrollToSection('resultPreview');
    }

    /**
     * @function renderOrderCard - 渲染订单卡片
     * @description 渲染单个订单的显示卡片
     * @param {object} order - 订单数据
     * @param {number} index - 订单索引
     * @returns {string} 订单卡片HTML
     */
    renderOrderCard(order, index) {
        return `
            <div class="order-card" data-order-index="${index}">
                <div class="order-header">
                    <h4>订单 ${index + 1}</h4>
                    <span class="order-reference">${order.ota_reference_number || '待生成'}</span>
                </div>
                <div class="order-details">
                    <div class="detail-row">
                        <label>客人姓名:</label>
                        <span>${order.customer_name || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <label>联系方式:</label>
                        <span>${order.customer_contact || '未提供'}</span>
                    </div>
                    <div class="detail-row">
                        <label>接送地点:</label>
                        <span>${order.pickup_location || '未知'} → ${order.drop_location || '未知'}</span>
                    </div>
                    <div class="detail-row">
                        <label>服务时间:</label>
                        <span>${order.service_date || '未知'} ${order.service_time || ''}</span>
                    </div>
                    <div class="detail-row">
                        <label>乘客人数:</label>
                        <span>${order.passenger_count || 1}人</span>
                    </div>
                    ${order.flight_number ? `
                    <div class="detail-row">
                        <label>航班信息:</label>
                        <span>${order.flight_number}</span>
                    </div>
                    ` : ''}
                    ${order.extra_requirement ? `
                    <div class="detail-row">
                        <label>特殊要求:</label>
                        <span>${order.extra_requirement}</span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    /**
     * @function displayCreateResults - 显示创建结果
     * @description 显示订单创建的结果
     * @param {Array} results - 创建结果数组
     */
    displayCreateResults(results) {
        const resultsDiv = document.getElementById('orderResults');
        if (!resultsDiv) return;

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.length - successCount;

        let html = `
            <div class="create-results-header">
                <h3>订单创建结果</h3>
                <div class="results-summary">
                    <span class="success-count">成功: ${successCount}</span>
                    <span class="failure-count">失败: ${failureCount}</span>
                    <span class="total-count">总计: ${results.length}</span>
                </div>
            </div>
        `;

        html += '<div class="create-results-list">';
        results.forEach((result, index) => {
            html += this.renderCreateResultCard(result, index);
        });
        html += '</div>';

        resultsDiv.innerHTML = html;
        window.app.uiManager.scrollToSection('resultPreview');
    }

    /**
     * @function renderCreateResultCard - 渲染创建结果卡片
     * @description 渲染单个订单创建结果的卡片
     * @param {object} result - 创建结果
     * @param {number} index - 结果索引
     * @returns {string} 结果卡片HTML
     */
    renderCreateResultCard(result, index) {
        const statusClass = result.success ? 'success' : 'failure';
        const statusIcon = result.success ? '✅' : '❌';
        const recoveredBadge = result.recovered ? '<span class="recovered-badge">已恢复</span>' : '';

        return `
            <div class="create-result-card ${statusClass}">
                <div class="result-header">
                    <span class="status-icon">${statusIcon}</span>
                    <h4>订单 ${index + 1}</h4>
                    ${recoveredBadge}
                </div>
                <div class="result-details">
                    <div class="customer-info">
                        <strong>${result.orderData.customer_name || '未知客人'}</strong>
                        <span>${result.orderData.pickup_location || '未知'} → ${result.orderData.drop_location || '未知'}</span>
                    </div>
                    ${result.success ? `
                        <div class="success-info">
                            <span class="order-id">订单ID: ${result.result.id || '未知'}</span>
                            <span class="reference">参考号: ${result.orderData.ota_reference_number || '未知'}</span>
                        </div>
                    ` : `
                        <div class="error-info">
                            <span class="error-message">${result.error || '未知错误'}</span>
                        </div>
                    `}
                </div>
            </div>
        `;
    }

    /**
     * @function createOrderEditForms - 创建订单编辑表单
     * @description 为每个处理的订单创建编辑表单
     */
    createOrderEditForms() {
        const editContainer = document.getElementById('manualEditContainer');
        if (!editContainer) return;

        let html = '<h3>编辑订单信息</h3>';

        if (this.appState.processedOrders.length > 0) {
            this.appState.processedOrders.forEach((order, index) => {
                html += this.createOrderEditForm(order, index);
            });
        } else {
            html += this.createOrderEditForm({}, 0);
        }

        editContainer.innerHTML = html;
    }

    /**
     * @function createOrderEditForm - 创建单个订单编辑表单
     * @description 创建单个订单的编辑表单
     * @param {object} order - 订单数据
     * @param {number} index - 订单索引
     * @returns {string} 表单HTML
     */
    createOrderEditForm(order, index) {
        return `
            <div class="order-edit-form" data-order-index="${index}">
                <h4>订单 ${index + 1}</h4>
                <form class="edit-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="customerName_${index}">客人姓名 *</label>
                            <input type="text" id="customerName_${index}" name="customerName" 
                                   value="${order.customer_name || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="customerContact_${index}">联系方式</label>
                            <input type="text" id="customerContact_${index}" name="customerContact" 
                                   value="${order.customer_contact || ''}">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pickup_${index}">接送地点 *</label>
                            <input type="text" id="pickup_${index}" name="pickup" 
                                   value="${order.pickup_location || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="destination_${index}">目的地 *</label>
                            <input type="text" id="destination_${index}" name="destination" 
                                   value="${order.drop_location || ''}" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_${index}">服务日期</label>
                            <input type="date" id="date_${index}" name="date" 
                                   value="${order.service_date || ''}">
                        </div>
                        <div class="form-group">
                            <label for="time_${index}">服务时间</label>
                            <input type="time" id="time_${index}" name="time" 
                                   value="${order.service_time || ''}">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="passengerNumber_${index}">乘客人数</label>
                            <input type="number" id="passengerNumber_${index}" name="passengerNumber" 
                                   value="${order.passenger_count || 1}" min="1">
                        </div>
                        <div class="form-group">
                            <label for="flightInfo_${index}">航班信息</label>
                            <input type="text" id="flightInfo_${index}" name="flightInfo" 
                                   value="${order.flight_number || ''}">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="extraRequirement_${index}">特殊要求</label>
                        <textarea id="extraRequirement_${index}" name="extraRequirement" 
                                  rows="2">${order.extra_requirement || ''}</textarea>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="backendUser_${index}">后台用户</label>
                            <select id="backendUser_${index}" name="backendUser">
                                <option value="">选择后台用户...</option>
                                ${this.renderBackendUserOptions(order.backend_user_id)}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="subCategory_${index}">服务类型</label>
                            <select id="subCategory_${index}" name="subCategory">
                                <option value="">选择服务类型...</option>
                                ${this.renderSubCategoryOptions(order.sub_category_id)}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="carType_${index}">车型</label>
                            <select id="carType_${index}" name="carType">
                                <option value="">选择车型...</option>
                                ${this.renderCarTypeOptions(order.car_type_id)}
                            </select>
                        </div>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * @function renderBackendUserOptions - 渲染后台用户选项
     * @description 渲染后台用户下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderBackendUserOptions(selectedId) {
        return this.appState.backendUsers.map(user => 
            `<option value="${user.id}" ${user.id === selectedId ? 'selected' : ''}>
                ${user.name} (${user.phone})
            </option>`
        ).join('');
    }

    /**
     * @function renderSubCategoryOptions - 渲染服务类型选项
     * @description 渲染服务类型下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderSubCategoryOptions(selectedId) {
        return this.appState.subCategories.map(category => 
            `<option value="${category.id}" ${category.id === selectedId ? 'selected' : ''}>
                ${category.name}
            </option>`
        ).join('');
    }

    /**
     * @function renderCarTypeOptions - 渲染车型选项
     * @description 渲染车型下拉选项
     * @param {number} selectedId - 选中的ID
     * @returns {string} 选项HTML
     */
    renderCarTypeOptions(selectedId) {
        return this.appState.carTypes.map(carType => 
            `<option value="${carType.id}" ${carType.id === selectedId ? 'selected' : ''}>
                ${carType.type} (${carType.seat_number}座)
            </option>`
        ).join('');
    }

    /**
     * @function addNewOrderForm - 添加新订单表单
     * @description 添加新的空白订单编辑表单
     */
    addNewOrderForm() {
        const editContainer = document.getElementById('manualEditContainer');
        if (!editContainer) return;

        const currentForms = editContainer.querySelectorAll('.order-edit-form');
        const newIndex = currentForms.length;
        
        const newFormHTML = this.createOrderEditForm({}, newIndex);
        editContainer.insertAdjacentHTML('beforeend', newFormHTML);
        
        window.app.uiManager.showInfo('已添加新订单表单');
        window.app.uiManager.scrollToSection('manualEditSection');
    }
}

// 导出到全局作用域（向后兼容）
window.OrderRenderer = OrderRenderer;

logger.info('模块', 'OrderRenderer模块加载完成', { version: 'v4.0.1' });
