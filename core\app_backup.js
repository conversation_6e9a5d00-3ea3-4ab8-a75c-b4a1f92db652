/**
 * @file app.js - OTA订单处理系统主应用（精简版）
 * @description 模块化重构后的主应用控制器，协调各个管理器模块
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 * @dependencies app-state.js, ui-manager.js, event-manager.js, order-processor.js, data-consistency-manager.js, error-recovery-manager.js
 */

/**
 * @class OTAOrderApp - OTA订单处理主应用类（精简版）
 * @description 协调各个模块，管理应用的整体流程和状态
 */
class OTAOrderApp {
    /**
     * @function constructor - 构造函数
     * @description 初始化主应用和所有管理器模块
     */
    constructor() {
        this.token = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.currentUserHash = null; // 新增：当前用户标识
        this.userDataCache = new Map(); // 新增：用户数据缓存
        this.backendUsers = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.processedOrders = [];
        
        // 初始化用户数据
        this.initializeUserData();
    }

    /**
     * @function initializeUserData - 初始化用户数据
     */
    initializeUserData() {
        if (this.userInfo) {
            this.currentUserHash = this.getUserHash();
            this.loadUserSystemData();
            logger.info('AppState', '用户数据初始化完成', {
                userHash: this.currentUserHash,
                userEmail: this.userInfo.email
            });
        } else {
            logger.debug('AppState', '无用户信息，使用兼容模式');
        }
    }

    /**
     * @function getUserHash - 生成用户标识哈希
     * @returns {string|null} 用户唯一标识
     */
    getUserHash() {
        if (!this.userInfo) return null;
        
        // 基于用户邮箱和ID生成哈希
        const userKey = `${this.userInfo.email || 'unknown'}_${this.userInfo.id || 'anonymous'}`;
        return btoa(userKey).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * @function setToken - 设置认证令牌
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN, token);
    }

    /**
     * @function setUserInfo - 设置用户信息并更新缓存策略
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        const oldUserHash = this.currentUserHash;
        const oldUserEmail = this.userInfo?.email;
        
        this.userInfo = userInfo;
        this.currentUserHash = this.getUserHash();
        
        // 检测用户切换
        if (oldUserHash && oldUserHash !== this.currentUserHash) {
            logger.info('AppState', '检测到用户切换', {
                oldUser: oldUserEmail,
                newUser: userInfo?.email,
                oldHash: oldUserHash,
                newHash: this.currentUserHash
            });
            
            // 清理旧用户数据
            this.clearUserSpecificData();
            
            // 触发用户切换事件（供主应用使用）
            this.triggerUserSwitchEvent(oldUserEmail, userInfo?.email);
        }
        
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
        
        // 加载新用户的系统数据
        if (this.currentUserHash) {
            this.loadUserSystemData();
        }
    }

    /**
     * @function triggerUserSwitchEvent - 触发用户切换事件
     * @param {string} oldUser - 旧用户邮箱
     * @param {string} newUser - 新用户邮箱
     */
    triggerUserSwitchEvent(oldUser, newUser) {
        const event = new CustomEvent('userSwitch', {
            detail: { oldUser, newUser }
        });
        window.dispatchEvent(event);
    }

    /**
     * @function clearAuth - 清除认证信息
     */
    clearAuth() {
        logger.info('AppState', '清除认证信息', { 
            userHash: this.currentUserHash 
        });
        
        this.token = null;
        this.userInfo = null;
        this.currentUserHash = null;
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO);
    }

    /**
     * @function cacheSystemData - 用户关联的系统数据缓存
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据缓存', { key });
            // 向后兼容：仍然更新实例属性和旧版存储
            this[key] = data;
            const storageKey = SYSTEM_CONFIG.STORAGE_KEYS[key.toUpperCase()];
            if (storageKey) {
                localStorage.setItem(storageKey, JSON.stringify(data));
            }
            return;
        }
        
        // 获取或创建用户缓存空间
        if (!this.userDataCache.has(this.currentUserHash)) {
            this.userDataCache.set(this.currentUserHash, {
                cacheTime: Date.now(),
                data: {}
            });
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        userCache.data[key] = data;
        userCache.lastUpdate = Date.now();
        
        // 持久化存储（添加用户前缀）
        const storageKey = `${this.currentUserHash}_${key}`;
        this.saveToStorage(storageKey, data);
        
        // 更新实例属性（向后兼容）
        this[key] = data;
        
        logger.debug('AppState', '用户数据缓存已更新', {
            userHash: this.currentUserHash,
            key,
            dataSize: data ? JSON.stringify(data).length : 0
        });
    }

    /**
     * @function getUserSystemData - 获取用户系统数据
     * @param {string} key - 数据键
     * @returns {any} 缓存数据
     */
    getUserSystemData(key) {
        if (!this.currentUserHash) {
            // 向后兼容：返回实例属性
            return this[key] || null;
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        return userCache?.data?.[key] || null;
    }

    /**
     * @function clearUserSpecificData - 清理用户特定数据
     */
    clearUserSpecificData() {
        logger.info('AppState', '开始清理用户特定数据', { 
            userHash: this.currentUserHash 
        });
        
        // 清理内存缓存
        this.backendUsers = [];
        this.subCategories = [];
        this.carTypes = [];
        this.processedOrders = [];
        
        // 清理当前用户的持久化缓存
        if (this.currentUserHash) {
            const keys = ['backendUsers', 'subCategories', 'carTypes'];
            keys.forEach(key => {
                const storageKey = `${this.currentUserHash}_${key}`;
                localStorage.removeItem(storageKey);
            });
            
            this.userDataCache.delete(this.currentUserHash);
        }
        
        logger.success('AppState', '用户特定数据清理完成');
    }

    /**
     * @function loadUserSystemData - 加载用户系统数据
     */
    loadUserSystemData() {
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据加载');
            return;
        }
        
        const keys = ['backendUsers', 'subCategories', 'carTypes'];
        let loadedCount = 0;
        
        keys.forEach(key => {
            const storageKey = `${this.currentUserHash}_${key}`;
            const data = this.loadFromStorage(storageKey);
            if (data && Array.isArray(data) && data.length > 0) {
                this[key] = data;
                
                // 更新用户缓存
                if (!this.userDataCache.has(this.currentUserHash)) {
                    this.userDataCache.set(this.currentUserHash, { 
                        cacheTime: Date.now(),
                        data: {} 
                    });
                }
                this.userDataCache.get(this.currentUserHash).data[key] = data;
                loadedCount++;
            }
        });
        
        logger.info('AppState', '用户系统数据加载完成', {
            userHash: this.currentUserHash,
            loadedCount,
            totalKeys: keys.length
        });
    }

    /**
     * @function saveToStorage - 保存数据到本地存储
     * @param {string} key - 存储键
     * @param {any} data - 数据
     */
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            logger.error('AppState', '数据存储失败', { key, error: error.message });
        }
    }

    /**
     * @function loadFromStorage - 从本地存储加载数据
     * @param {string} key - 存储键
     * @returns {any} 存储的数据
     */
    loadFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            logger.error('AppState', '数据加载失败', { key, error: error.message });
            return null;
        }
    }

    /**
     * @function getCacheInfo - 获取缓存信息
     * @returns {object} 缓存状态信息
     */
    getCacheInfo() {
        return {
            currentUserHash: this.currentUserHash,
            userCount: this.userDataCache.size,
            currentUserData: this.currentUserHash ? 
                this.userDataCache.get(this.currentUserHash) : null,
            hasLegacyData: !this.currentUserHash && (
                this.backendUsers.length > 0 || 
                this.subCategories.length > 0 || 
                this.carTypes.length > 0
            )
        };
    }

    /**
     * @function validateDataIntegrity - 验证数据完整性
     * @returns {object} 验证结果
     */
    validateDataIntegrity() {
        const requiredKeys = ['backendUsers', 'subCategories', 'carTypes'];
        const validation = {
            isValid: true,
            missingData: [],
            emptyData: [],
            userHash: this.currentUserHash
        };
        
        requiredKeys.forEach(key => {
            const data = this.getUserSystemData(key) || this[key];
            if (!data) {
                validation.missingData.push(key);
                validation.isValid = false;
            } else if (Array.isArray(data) && data.length === 0) {
                validation.emptyData.push(key);
                validation.isValid = false;
            }
        });
        
        return validation;
    }
}

// 主应用类
class OTAOrderApp {
    constructor() {
        this.appState = new AppState();
        this.apiService = new ApiService(this.appState);
        this.llmService = new LLMService();
        this.orderParser = new OrderParser(this.llmService);
        this.imageService = new ImageService();
        
        // 新增：数据管理器
        this.dataConsistencyManager = new DataConsistencyManager(this.appState, this.apiService);
        this.errorRecoveryManager = new ErrorRecoveryManager(this.appState, this.apiService, this.dataConsistencyManager);
        
        this.isInitialized = false;
        
        // 绑定用户切换事件
        this.bindUserSwitchEvent();
    }

    /**
     * @function bindUserSwitchEvent - 绑定用户切换事件
     */
    bindUserSwitchEvent() {
        window.addEventListener('userSwitch', (event) => {
            this.handleUserSwitch(event.detail);
        });
    }

    /**
     * @function handleUserSwitch - 处理用户切换
     * @param {object} detail - 切换详情
     */
    async handleUserSwitch(detail) {
        logger.info('应用', '处理用户切换事件', detail);
        
        try {
            // 清空UI显示
            this.clearUIDisplays();
            
            // 重置智能选择服务
            if (window.smartSelection) {
                window.smartSelection.resetToDefaults();
            }
            
            // 显示切换提示
            const confirmed = await this.showUserSwitchConfirmation(detail);
            if (confirmed) {
                // 重新加载系统数据
                await this.loadSystemData();
                
                // 更新UI选择器
                this.updateUISelectors();
                
                logger.success('应用', '用户切换处理完成');
            }
        } catch (error) {
            logger.error('应用', '用户切换处理失败', error);
        }
    }

    /**
     * @function showUserSwitchConfirmation - 显示用户切换确认
     * @param {object} switchInfo - 切换信息
     * @returns {Promise<boolean>} 用户确认结果
     */
    async showUserSwitchConfirmation(switchInfo) {
        return new Promise((resolve) => {
            const message = `检测到账号切换：\n从 ${switchInfo.oldUser} 切换到 ${switchInfo.newUser}\n\n系统将清理旧用户数据并重新加载。是否继续？`;
            
            if (confirm(message)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }

    /**
     * @function clearUIDisplays - 清空UI显示内容
     */
    clearUIDisplays() {
        // 清空选择器
        this.clearAllSelectors();
        
        // 隐藏结果区域
        const sections = ['resultPreview', 'orderStatus', 'manualEditSection'];
        sections.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.classList.add('hidden');
        });
        
        // 清空表单
        const forms = document.querySelectorAll('.order-edit-form');
        forms.forEach(form => form.remove());
        
        // 清空结果显示
        const resultsDiv = document.getElementById('orderResults');
        if (resultsDiv) resultsDiv.innerHTML = '';
        
        logger.debug('应用', 'UI显示内容清理完成');
    }

    /**
     * @function clearAllSelectors - 清空所有选择器
     */
    clearAllSelectors() {
        const selectors = ['backendUserSelect', 'subCategorySelect', 'carTypeSelect'];
        selectors.forEach(id => {
            const selector = document.getElementById(id);
            if (selector) {
                selector.innerHTML = '<option value="">选择...</option>';
            }
        });
    }

    /**
     * @function initialize - 初始化应用
     */
    async initialize() {
        if (this.isInitialized) return;

        logger.info('应用', '开始初始化OTA订单处理系统');

        try {
            // 1. 初始化UI组件
            this.initializeUI();

            // 2. 绑定事件监听器
            this.bindEventListeners();

            // 3. 检查登录状态
            await this.checkAuthStatus();

            // 4. 初始化LLM连接检测
            this.initializeLLMStatus();

            // 5. 加载系统数据
            await this.loadSystemData();

            // 6. 初始化智能选择服务
            this.initializeSmartSelection();

            // 7. 初始化地址搜索服务
            this.initializeAddressSearchService();

            this.isInitialized = true;
            logger.success('应用', '系统初始化完成');

        } catch (error) {
            logger.error('应用', '系统初始化失败', { error: error.message });
            throw error;
        }
    }

    /**
     * @function initializeUI - 初始化UI组件
     */
    initializeUI() {
        // 显示登录模态框（如果未登录）
        if (!this.appState.token) {
            this.showLoginModal();
        }

        // 初始化状态指示器
        this.updateConnectionStatus();

        // 初始化文件上传区域
        this.initializeFileUpload();

        logger.debug('应用', 'UI组件初始化完成');
    }

    /**
     * @function bindEventListeners - 绑定事件监听器
     */
    bindEventListeners() {
        // 登录表单
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // 处理订单按钮
        const processBtn = document.getElementById('processBtn');
        if (processBtn) {
            processBtn.addEventListener('click', () => this.handleProcessOrder());
        }

        // 创建订单按钮
        const createBtn = document.getElementById('createOrderBtn');
        if (createBtn) {
            createBtn.addEventListener('click', () => this.handleCreateOrders());
        }

        // 文件上传
        const fileInput = document.getElementById('imageFile');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        }

        // 拖拽上传
        const dropZone = document.getElementById('uploadArea');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            dropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
        }

        // LLM状态指示器点击事件
        const geminiIndicator = document.getElementById('geminiStatusIndicator');
        if (geminiIndicator) {
            geminiIndicator.addEventListener('click', () => this.handleLLMStatusClick('gemini'));
        }

        // 其他按钮事件
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', () => this.handleLogout());
        }

        const editBtn = document.getElementById('editBtn');
        if (editBtn) {
            editBtn.addEventListener('click', () => this.handleEditResults());
        }

        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.handleRefreshResults());
        }

        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.handleExportResults());
        }

        // 手动编辑相关按钮
        const addOrderBtn = document.getElementById('addOrderBtn');
        if (addOrderBtn) {
            addOrderBtn.addEventListener('click', () => this.handleAddOrder());
        }

        const reAnalyzeBtn = document.getElementById('reAnalyzeBtn');
        if (reAnalyzeBtn) {
            reAnalyzeBtn.addEventListener('click', () => this.handleReAnalyze());
        }

        logger.debug('应用', '事件监听器绑定完成');
    }

    /**
     * @function checkAuthStatus - 检查认证状态
     */
    async checkAuthStatus() {
        if (this.appState.token) {
            logger.info('应用', '发现已保存的认证令牌，验证有效性');
            try {
                // 尝试获取用户数据来验证token有效性
                await this.apiService.getBackendUsers();
                logger.success('应用', '认证令牌有效');
                this.hideLoginModal();
            } catch (error) {
                logger.warn('应用', '认证令牌已失效，需要重新登录');
                this.appState.clearAuth();
                this.showLoginModal();
            }
        }
    }

    /**
     * @function initializeLLMStatus - 初始化LLM状态检测
     */
    initializeLLMStatus() {
        // 立即检测一次
        this.checkLLMConnections();

        // 定期检测（每5分钟）
        setInterval(() => {
            this.checkLLMConnections();
        }, 5 * 60 * 1000);
    }

    /**
     * @function checkLLMConnections - 检测LLM连接状态
     */
    async checkLLMConnections() {
        logger.info('应用', '检测LLM服务连接状态');

        try {
            // 检测Gemini连接
            const geminiStatus = await this.llmService.checkGeminiConnection();

            // 更新UI状态指示器
            this.updateLLMStatusUI(geminiStatus);

        } catch (error) {
            logger.error('应用', 'LLM连接检测失败', { error: error.message });
        }
    }

    /**
     * @function loadSystemData - 加载系统数据
     */
    async loadSystemData() {
        if (!this.appState.token) return;

        logger.info('应用', '加载系统数据');

        try {
            // 并行加载所有系统数据
            await Promise.all([
                this.apiService.getBackendUsers(),
                this.apiService.getSubCategories(),
                this.apiService.getCarTypes()
            ]);

            // 更新UI选择器
            this.updateUISelectors();

            logger.success('应用', '系统数据加载完成');

        } catch (error) {
            logger.error('应用', '系统数据加载失败', { error: error.message });
        }
    }

    /**
     * @function handleLogin - 处理登录
     * @param {Event} event - 表单提交事件
     */
    async handleLogin(event) {
        event.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        logger.info('应用', '开始用户登录', { email });

        try {
            const result = await this.apiService.login(email, password);
            
            if (result.success) {
                logger.success('应用', '登录成功');
                this.hideLoginModal();
                await this.loadSystemData();
            }

        } catch (error) {
            logger.error('应用', '登录失败', { error: error.message });
            this.showError('登录失败: ' + error.message);
        }
    }

    /**
     * @function handleProcessOrder - 处理订单
     */
    async handleProcessOrder() {
        const textInput = document.getElementById('orderText').value.trim();
        const otaType = document.getElementById('otaSelect').value;

        if (!textInput) {
            this.showError('请输入订单文本');
            return;
        }

        logger.info('应用', '开始处理订单', {
            textLength: textInput.length,
            otaType: otaType
        });

        try {
            this.showLoading('正在处理订单...');

            const result = await this.orderParser.parseOrders(textInput, otaType);

            if (result.success) {
                this.appState.processedOrders = result.orders;
                this.displayOrderResults(result);
                logger.success('应用', '订单处理完成', {
                    orderCount: result.orders.length
                });
            } else {
                throw new Error(result.error || '订单处理失败');
            }

        } catch (error) {
            logger.error('应用', '订单处理失败', { error: error.message });
            this.showError('订单处理失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * @function handleCreateOrders - 创建订单（集成错误恢复机制）
     */
    async handleCreateOrders() {
        try {
            logger.info('订单', '开始创建订单流程');
            
            // 1. 数据一致性预检查
            const isDataValid = await this.dataConsistencyManager.validateUserData();
            if (!isDataValid) {
                logger.warn('订单', '数据一致性检查失败，正在刷新数据...');
                await this.dataConsistencyManager.forceDataRefresh();
                
                // 更新UI选择器
                this.updateUISelectors();
                
                // 提示用户
                this.showError('系统数据已更新，请重新检查选择项');
                return;
            }

            // 显示加载状态
            this.showLoading('正在创建订单...');
            
            // 收集订单数据
            const orders = await this.collectAllOrders();
            
            if (!orders || orders.length === 0) {
                this.hideLoading();
                this.showError('没有找到要创建的订单');
                return;
            }

            logger.info('订单', `准备创建 ${orders.length} 个订单`);

            // 创建结果存储
            const results = [];
            
            // 逐个处理订单（支持错误恢复）
            for (let i = 0; i < orders.length; i++) {
                const order = orders[i];
                logger.debug('订单', `正在处理第 ${i + 1} 个订单`, order);

                try {
                    // 尝试创建订单
                    const result = await this.apiService.createOrder(order);
                    results.push({ 
                        success: true, 
                        result, 
                        orderData: order,
                        index: i 
                    });
                    logger.success('订单', `第 ${i + 1} 个订单创建成功`, result);

                } catch (error) {
                    logger.error('订单', `第 ${i + 1} 个订单创建失败`, error);
                    
                    // 错误分析和恢复尝试
                    const errorAnalysis = this.errorRecoveryManager.analyzeError(error);
                    
                    if (errorAnalysis.recoverable) {
                        logger.info('订单', '尝试错误恢复', errorAnalysis);
                        
                        const recoveryResult = await this.errorRecoveryManager.attemptRecovery(
                            errorAnalysis, 
                            order
                        );
                        
                        if (recoveryResult.success) {
                            results.push({ 
                                success: true, 
                                result: recoveryResult.result, 
                                orderData: recoveryResult.orderData,
                                index: i,
                                recovered: true 
                            });
                            logger.success('订单', `第 ${i + 1} 个订单错误恢复成功`);
                        } else {
                            results.push({ 
                                success: false, 
                                error: this.formatErrorMessage(error, recoveryResult),
                                orderData: order,
                                index: i,
                                errorAnalysis 
                            });
                        }
                    } else {
                        // 不可恢复的错误
                        results.push({ 
                            success: false, 
                            error: this.formatErrorMessage(error),
                            orderData: order,
                            index: i,
                            errorAnalysis 
                        });
                    }
                }
            }

            // 隐藏加载状态
            this.hideLoading();

            // 显示创建结果
            this.displayCreateResults(results);

            // 如果有成功的订单，添加到状态
            const successfulOrders = results.filter(r => r.success);
            if (successfulOrders.length > 0) {
                this.appState.processedOrders.push(...successfulOrders.map(r => r.orderData));
                
                // 显示成功通知
                if (window.notificationManager) {
                    window.notificationManager.success(
                        '订单创建成功',
                        `成功创建 ${successfulOrders.length}/${results.length} 个订单`
                    );
                }
            }

        } catch (error) {
            logger.error('订单', '创建订单流程失败', error);
            this.hideLoading();
            this.showError('创建订单过程中发生错误: ' + error.message);
        }
    }

    /**
     * @function formatErrorMessage - 格式化错误消息
     * @param {Error} error - 原始错误
     * @param {object} recoveryResult - 恢复结果（可选）
     * @returns {string} 格式化的错误消息
     */
    formatErrorMessage(error, recoveryResult = null) {
        let message = error.message || '未知错误';
        
        // 添加错误类型信息
        if (error.response?.data?.validation_error) {
            message = '数据验证失败: ' + message;
        } else if (error.response?.status === 401) {
            message = '认证失败，请重新登录';
        } else if (error.response?.status === 403) {
            message = '权限不足';
        }
        
        // 添加恢复失败信息
        if (recoveryResult && !recoveryResult.success) {
            message += ` (自动恢复失败: ${recoveryResult.reason})`;
        }
        
        return message;
    }

    /**
     * @function handleFileUpload - 处理文件上传
     * @param {Event} event - 文件选择事件
     */
    async handleFileUpload(event) {
        const files = event.target.files;
        if (!files || files.length === 0) return;

        await this.processUploadedFiles(files);
    }

    /**
     * @function handleFileDrop - 处理文件拖拽
     * @param {Event} event - 拖拽事件
     */
    async handleFileDrop(event) {
        event.preventDefault();
        const files = event.dataTransfer.files;
        if (!files || files.length === 0) return;

        await this.processUploadedFiles(files);
    }

    /**
     * @function handleLogout - 处理用户退出登录
     */
    handleLogout() {
        logger.info('应用', '用户退出登录');

        // 清除认证信息
        this.appState.clearAuth();

        // 显示登录模态框
        this.showLoginModal();

        // 清空处理结果
        this.appState.processedOrders = [];

        // 隐藏结果区域
        const resultSection = document.getElementById('resultPreview');
        const statusSection = document.getElementById('orderStatus');

        if (resultSection) {
            resultSection.classList.add('hidden');
        }

        if (statusSection) {
            statusSection.classList.add('hidden');
        }

        logger.success('应用', '退出登录完成');
    }

    /**
     * @function handleEditResults - 处理编辑结果
     */
    handleEditResults() {
        logger.info('应用', '编辑处理结果');
        
        const manualEditSection = document.getElementById('manualEditSection');
        const orderEditForms = document.getElementById('orderEditForms');
        
        if (!manualEditSection || !orderEditForms) {
            this.showError('编辑界面初始化失败');
            return;
        }
        
        // 显示编辑区域
        manualEditSection.classList.remove('hidden');
        
        // 清空现有表单
        orderEditForms.innerHTML = '';
        
        // 如果有已处理的订单，为每个订单创建编辑表单
        if (this.appState.processedOrders && this.appState.processedOrders.length > 0) {
            this.appState.processedOrders.forEach((order, index) => {
                // 将下划线命名转换为驼峰命名以适配表单
                const formData = {
                    customerName: order.customer_name || order.customerName || '',
                    customerContact: order.customer_contact || order.customerContact || '',
                    flightInfo: order.flight_number || order.flightNumber || '',
                    serviceType: this.mapServiceType(order.service_type || order.serviceType),
                    pickup: order.pickup_location || order.pickupLocation || '',
                    destination: order.drop_location || order.dropLocation || '',
                    date: order.service_date || order.serviceDate || '',
                    time: order.service_time || order.serviceTime || '',
                    passengerNumber: order.passenger_count || order.passengerCount || 1,
                    meetAndGreet: false, // 默认值
                    extraRequirement: order.extra_requirement || order.extraRequirement || ''
                };
                
                this.createOrderEditForm(formData);
            });
        } else {
            // 如果没有已处理的订单，创建一个空表单
            this.createOrderEditForm();
        }
        
        // 滚动到编辑区域
        manualEditSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    /**
     * @function mapServiceType - 映射服务类型
     * @param {string} serviceType - 原始服务类型
     * @returns {string} 映射后的服务类型
     */
    mapServiceType(serviceType) {
        if (!serviceType) return '';
        
        const typeMapping = {
            'airport_pickup': 'pickup',
            'airport_dropoff': 'dropoff',
            'pickup': 'pickup',
            'dropoff': 'dropoff',
            'charter': 'charter',
            'transfer': 'transfer',
            '接机': 'pickup',
            '送机': 'dropoff',
            '包车': 'charter',
            '点对点': 'transfer'
        };
        
        return typeMapping[serviceType.toLowerCase()] || serviceType;
    }

    /**
     * @function handleRefreshResults - 处理重新处理
     */
    async handleRefreshResults() {
        logger.info('应用', '重新处理订单');

        const textInput = document.getElementById('orderText').value.trim();
        const otaType = document.getElementById('otaSelect').value;

        if (!textInput) {
            this.showError('没有订单文本可以重新处理');
            return;
        }

        // 重新处理订单
        await this.handleProcessOrder();
    }

    /**
     * @function handleExportResults - 处理导出结果
     */
    handleExportResults() {
        logger.info('应用', '导出处理结果');

        if (!this.appState.processedOrders || this.appState.processedOrders.length === 0) {
            this.showError('没有可导出的订单数据');
            return;
        }

        try {
            // 生成导出数据
            const exportData = {
                exportTime: new Date().toISOString(),
                orderCount: this.appState.processedOrders.length,
                orders: this.appState.processedOrders
            };

            // 创建下载链接
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            // 创建下载链接
            const link = document.createElement('a');
            link.href = url;
            link.download = `ota-orders-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清理URL对象
            URL.revokeObjectURL(url);

            logger.success('应用', '导出完成', {
                orderCount: this.appState.processedOrders.length,
                fileName: link.download
            });

        } catch (error) {
            logger.error('应用', '导出失败', { error: error.message });
            this.showError('导出失败: ' + error.message);
        }
    }

    /**
     * @function handleLLMStatusClick - 处理LLM状态指示器点击
     * @param {string} llmType - LLM类型 ('gemini')
     */
    async handleLLMStatusClick(llmType) {
        logger.info('应用', `手动检测${llmType}连接状态`);

        try {
            // 显示检测中状态
            this.setLLMStatusChecking(llmType);

            let isConnected = false;
            if (llmType === 'gemini') {
                isConnected = await this.llmService.checkGeminiConnection();
            }

            // 更新单个LLM的状态
            this.updateSingleLLMStatus(llmType, isConnected);

            logger.success('应用', `${llmType}连接检测完成`, { isConnected });

        } catch (error) {
            logger.error('应用', `${llmType}连接检测失败`, { error: error.message });
            this.updateSingleLLMStatus(llmType, false);
        }
    }

    /**
     * @function setLLMStatusChecking - 设置LLM状态为检测中
     * @param {string} llmType - LLM类型
     */
    setLLMStatusChecking(llmType) {
        const light = document.getElementById('geminiStatusLight');
        const text = document.getElementById('geminiStatusText');

        if (light) {
            light.className = 'status-light checking';
        }

        if (text) {
            text.textContent = 'Gemini 检测中...';
        }
    }

    /**
     * @function updateSingleLLMStatus - 更新单个LLM状态
     * @param {string} llmType - LLM类型
     * @param {boolean} isConnected - 连接状态
     */
    updateSingleLLMStatus(llmType, isConnected) {
        const light = document.getElementById('geminiStatusLight');
        const text = document.getElementById('geminiStatusText');
        const indicator = document.getElementById('geminiStatusIndicator');

        if (light) {
            light.className = isConnected ? 'status-light connected' : 'status-light disconnected';
        }

        if (text) {
            text.textContent = isConnected ? 'Gemini 已连接' : 'Gemini 连接失败';
        }

        if (indicator) {
            indicator.title = isConnected ? `${displayName} API 连接正常` : `${displayName} API 连接失败`;
            const baseClass = `llm-status-indicator ${llmType}-indicator`;
            indicator.className = isConnected ? `${baseClass} connected` : `${baseClass} disconnected`;
        }
    }

    /**
     * @function processUploadedFiles - 处理上传的文件
     * @param {FileList} files - 文件列表
     */
    async processUploadedFiles(files) {
        logger.info('应用', '开始处理上传的文件', { fileCount: files.length });

        try {
            this.showLoading('正在处理图片...');

            // 处理图片文件
            const imageResults = await this.imageService.processImageFiles(files);

            if (imageResults.success) {
                // 从图片中提取订单信息
                const orderResults = await this.imageService.extractOrdersFromImages(imageResults.results);

                if (orderResults.success) {
                    // 将提取的文本填入文本框
                    document.getElementById('orderText').value = orderResults.combinedText;
                    
                    // 自动处理订单
                    this.appState.processedOrders = orderResults.orders;
                    this.displayOrderResults(orderResults);
                }
            }

            this.displayImageResults(imageResults);

        } catch (error) {
            logger.error('应用', '文件处理失败', { error: error.message });
            this.showError('文件处理失败: ' + error.message);
        } finally {
            this.hideLoading();
        }
    }

    // UI辅助方法
    /**
     * @function showLoginModal - 显示登录模态框
     */
    showLoginModal() {
        const modal = document.getElementById('loginModal');
        const mainApp = document.getElementById('mainApp');

        if (modal) {
            modal.style.display = 'block';
            logger.debug('UI', '显示登录模态框');
        }

        // 隐藏主界面
        if (mainApp) {
            mainApp.classList.add('hidden');
            logger.debug('UI', '隐藏主界面');
        }
    }

    /**
     * @function hideLoginModal - 隐藏登录模态框
     */
    hideLoginModal() {
        const modal = document.getElementById('loginModal');
        const mainApp = document.getElementById('mainApp');

        if (modal) {
            modal.style.display = 'none';
            logger.debug('UI', '隐藏登录模态框');
        }

        // 显示主界面
        if (mainApp) {
            mainApp.classList.remove('hidden');
            logger.debug('UI', '显示主界面');
        }
    }

    /**
     * @function showLoading - 显示加载状态
     * @param {string} message - 加载消息
     */
    showLoading(message) {
        const loadingModal = document.getElementById('loadingModal');
        const messageDiv = document.getElementById('loadingText');

        if (loadingModal) {
            loadingModal.classList.remove('hidden');
            loadingModal.style.display = 'block';
        }

        if (messageDiv && message) {
            messageDiv.textContent = message;
        }

        logger.debug('UI', '显示加载状态', { message });
    }

    /**
     * @function hideLoading - 隐藏加载状态
     */
    hideLoading() {
        const loadingModal = document.getElementById('loadingModal');
        if (loadingModal) {
            loadingModal.classList.add('hidden');
            loadingModal.style.display = 'none';
        }
        logger.debug('UI', '隐藏加载状态');
    }

    /**
     * @function showError - 显示错误消息
     * @param {string} message - 错误消息
     */
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 也在控制台显示错误
        logger.error('UI', '显示错误消息', { message });

        // 3秒后自动隐藏错误消息
        setTimeout(() => {
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        }, 3000);
    }

    /**
     * @function updateConnectionStatus - 更新连接状态
     */
    updateConnectionStatus() {
        // 这里可以添加连接状态更新逻辑
        logger.debug('UI', '更新连接状态');
    }

    /**
     * @function updateLLMStatusUI - 更新LLM状态UI
     * @param {boolean} gemini - Gemini连接状态
     */
    updateLLMStatusUI(gemini) {
        // 更新Gemini状态指示器
        const geminiLight = document.getElementById('geminiStatusLight');
        const geminiText = document.getElementById('geminiStatusText');
        const geminiIndicator = document.getElementById('geminiStatusIndicator');

        if (geminiLight) {
            geminiLight.className = gemini ? 'status-light connected' : 'status-light disconnected';
        }

        if (geminiText) {
            geminiText.textContent = gemini ? 'Gemini 已连接' : 'Gemini 连接失败';
        }

        if (geminiIndicator) {
            geminiIndicator.title = gemini ? 'Gemini API 连接正常' : 'Gemini API 连接失败';
            geminiIndicator.className = gemini ?
                'llm-status-indicator gemini-indicator connected' :
                'llm-status-indicator gemini-indicator disconnected';
        }

        logger.debug('UI', '更新LLM状态UI', {
            gemini,
            geminiElements: {
                light: !!geminiLight,
                text: !!geminiText,
                indicator: !!geminiIndicator
            }
        });
    }

    /**
     * @function updateUISelectors - 更新UI选择器
     */
    updateUISelectors() {
        // 更新后台用户选择器
        this.updateBackendUserSelector();

        // 更新子分类选择器
        this.updateSubCategorySelector();

        // 更新车型选择器
        this.updateCarTypeSelector();

        logger.debug('UI', 'UI选择器更新完成');
    }

    /**
     * @function updateBackendUserSelector - 更新后台用户选择器
     */
    updateBackendUserSelector() {
        const selector = document.getElementById('backendUserSelect');
        if (selector && this.appState.backendUsers) {
            selector.innerHTML = '<option value="">选择用户</option>';

            this.appState.backendUsers.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.role_id})`;
                selector.appendChild(option);
            });
        }
    }

    /**
     * @function updateSubCategorySelector - 更新子分类选择器（简化版本）
     * 注意：服务类型选择器已简化为三种基本类型，不再动态填充API数据
     */
    updateSubCategorySelector() {
        const selector = document.getElementById('subCategorySelect');
        if (selector) {
            // 保持HTML中定义的简化选项，不覆盖
            // 服务类型选择器现在使用静态的三种基本类型：pickup, dropoff, charter
            logger.info('应用', '服务类型选择器保持简化版本（pickup/dropoff/charter）');
        }
    }

    /**
     * @function updateCarTypeSelector - 更新车型选择器
     */
    updateCarTypeSelector() {
        const selector = document.getElementById('carTypeSelect');
        if (selector && this.appState.carTypes) {
            selector.innerHTML = '<option value="">选择车型</option>';

            this.appState.carTypes.forEach(carType => {
                const option = document.createElement('option');
                option.value = carType.id;
                option.textContent = `${carType.type} (${carType.seat_number}座)`;
                selector.appendChild(option);
            });
        }
    }

    /**
     * @function displayOrderResults - 显示订单结果
     * @param {object} results - 订单处理结果
     */
    displayOrderResults(results) {
        const resultsDiv = document.getElementById('orderResults');
        const resultSection = document.getElementById('resultPreview');
        const manualEditSection = document.getElementById('manualEditSection');

        if (!resultsDiv) return;

        // 总是显示结果预览区域
        if (resultSection) {
            resultSection.classList.remove('hidden');
        }

        if (results.success && results.orders && results.orders.length > 0) {
            // 有订单时显示处理结果
            let html = '<h3>处理结果</h3>';
            html += `<p>共处理 ${results.orders.length} 个订单</p>`;
            html += `<p><strong>OTA类型:</strong> ${results.otaType || '未知'}</p>`;
            html += `<p><strong>处理时间:</strong> ${results.processingTime || 0}ms</p>`;

            results.orders.forEach((order, index) => {
                // 字段映射：将下划线命名转换为驼峰命名
                const displayOrder = {
                    customerName: order.customer_name || order.customerName || '未知',
                    serviceType: order.service_type || order.serviceType || '未知',
                    serviceDate: order.service_date || order.serviceDate || '未知',
                    flightNumber: order.flight_number || order.flightNumber || '未知',
                    passengerCount: order.passenger_count || order.passengerCount || '未知',
                    pickupLocation: order.pickup_location || order.pickupLocation || '未知',
                    dropLocation: order.drop_location || order.dropLocation || '未知',
                    serviceTime: order.service_time || order.serviceTime || '未知',
                    carType: order.car_type || order.carType || '未知',
                    otaPrice: order.ota_price || order.otaPrice || '未知',
                    driverFee: order.driver_fee || order.driverFee || '未知',
                    extraRequirement: order.extra_requirement || order.extraRequirement || '无',
                    other: order.other || '无',
                    // 新增字段
                    customerEmail: order.customer_email || order.customerEmail || '未知',
                    customerContact: order.customer_contact || order.customerContact || this.generateRandomContact(),
                    otaReferenceNumber: order.ota_reference_number || order.otaReferenceNumber || '未知'
                };
                
                // 获取当前选择器的值
                const currentSelections = this.getCurrentSelections();
                
                html += `<div class="order-result">`;
                html += `<h4>订单 ${index + 1}</h4>`;
                
                // 显示智能选择状态和结果
                const smartSelectionStatus = window.smartSelection && window.smartSelection.initialized ? '✅ 运行中' : '❌ 未运行';
                html += `<p><strong>🤖 智能选择状态:</strong> ${smartSelectionStatus}</p>`;

                // 显示智能选择结果或当前选择器状态
                html += `<div class="current-selections">`;
                html += `<h5>📋 选择状态:</h5>`;

                // 检查订单是否有智能选择结果
                if (order._smartSelection) {
                    const smartResult = order._smartSelection;
                    html += `<div class="smart-selection-results">`;
                    html += `<p><strong>🤖 智能选择结果:</strong></p>`;
                    html += `<p><strong>负责用户:</strong> ${smartResult.backendUser?.name || '未选择'} ${smartResult.backendUser?.confidence ? `(置信度: ${Math.round(smartResult.backendUser.confidence * 100)}%)` : ''}</p>`;
                    html += `<p><strong>服务分类:</strong> ${smartResult.subCategory?.name || '未选择'} ${smartResult.subCategory?.confidence ? `(置信度: ${Math.round(smartResult.subCategory.confidence * 100)}%)` : ''}</p>`;
                    html += `<p><strong>车型:</strong> ${smartResult.carType?.type || '未选择'} ${smartResult.carType?.confidence ? `(置信度: ${Math.round(smartResult.carType.confidence * 100)}%)` : ''}</p>`;

                    if (smartResult.reasoning) {
                        html += `<div class="selection-reasoning">`;
                        html += `<p><strong>💡 选择原因:</strong></p>`;
                        html += `<ul>`;
                        if (smartResult.reasoning.backendUser) {
                            html += `<li><strong>负责用户:</strong> ${smartResult.reasoning.backendUser}</li>`;
                        }
                        if (smartResult.reasoning.subCategory) {
                            html += `<li><strong>服务分类:</strong> ${smartResult.reasoning.subCategory}</li>`;
                        }
                        if (smartResult.reasoning.carType) {
                            html += `<li><strong>车型:</strong> ${smartResult.reasoning.carType}</li>`;
                        }
                        html += `</ul>`;
                        html += `</div>`;
                    }
                    html += `</div>`;
                } else {
                    // 显示当前选择器状态（默认值）
                    html += `<div class="manual-selection-status">`;
                    html += `<p><strong>📝 手动选择状态:</strong></p>`;
                    html += `<p><strong>负责用户:</strong> ${currentSelections.backendUser}</p>`;
                    html += `<p><strong>服务分类:</strong> ${currentSelections.subCategory}</p>`;
                    html += `<p><strong>车型:</strong> ${currentSelections.carType}</p>`;
                    html += `<p class="selection-note">💡 此订单未使用智能选择，显示为当前选择器设置</p>`;
                    html += `</div>`;
                }

                html += `</div>`;
                
                html += `<p><strong>客人姓名:</strong> ${displayOrder.customerName}</p>`;
                html += `<p><strong>电邮:</strong> ${displayOrder.customerEmail}</p>`;
                html += `<p><strong>手机号:</strong> ${displayOrder.customerContact}</p>`;
                html += `<p><strong>OTA参考号:</strong> ${displayOrder.otaReferenceNumber}</p>`;
                html += `<p><strong>服务类型:</strong> ${displayOrder.serviceType}</p>`;
                html += `<p><strong>服务日期:</strong> ${displayOrder.serviceDate}</p>`;
                html += `<p><strong>服务时间:</strong> ${displayOrder.serviceTime}</p>`;
                html += `<p><strong>航班信息:</strong> ${displayOrder.flightNumber}</p>`;
                html += `<p><strong>乘客人数:</strong> ${displayOrder.passengerCount}</p>`;
                html += `<p><strong>上车地点:</strong> ${displayOrder.pickupLocation}</p>`;
                html += `<p><strong>下车地点:</strong> ${displayOrder.dropLocation}</p>`;
                html += `<p><strong>车型:</strong> ${displayOrder.carType}</p>`;
                html += `<p><strong>OTA价格:</strong> ${displayOrder.otaPrice}</p>`;
                html += `<p><strong>司机费用:</strong> ${displayOrder.driverFee}</p>`;
                html += `<p><strong>额外要求:</strong> ${displayOrder.extraRequirement}</p>`;
                if (displayOrder.other && displayOrder.other !== '无') {
                    html += `<p><strong>其他信息:</strong> ${displayOrder.other}</p>`;
                }
                
                // 显示智能选择结果
                if (order._smartSelectionDisplay) {
                    html += order._smartSelectionDisplay;
                }
                
                html += `</div>`;
            });

            resultsDiv.innerHTML = html;

            // 隐藏手动编辑区域
            if (manualEditSection) {
                manualEditSection.classList.add('hidden');
            }
        } else {
            // 没有订单时显示友好提示和手动编辑选项
            let html = '<h3>处理结果</h3>';
            html += `<div class="no-orders-message">`;
            html += `<p><strong>⚠️ 未能自动识别订单信息</strong></p>`;
            html += `<p>OTA类型: ${results.otaType || '未知'}</p>`;
            html += `<p>处理时间: ${results.processingTime || 0}ms</p>`;

            if (results.otaType === 'other') {
                html += `<p class="suggestion">💡 建议：尝试选择具体的OTA类型重新分析，或手动输入订单信息</p>`;
            } else {
                html += `<p class="suggestion">💡 建议：检查订单格式是否正确，或手动输入订单信息</p>`;
            }

            html += `</div>`;
            resultsDiv.innerHTML = html;

            // 显示手动编辑区域
            if (manualEditSection) {
                manualEditSection.classList.remove('hidden');
                this.initializeManualEdit(results);
            }
        }

        logger.debug('UI', '显示订单结果', {
            orderCount: results.orders?.length || 0,
            otaType: results.otaType,
            showManualEdit: !results.orders || results.orders.length === 0
        });
    }

    /**
     * @function displayCreateResults - 显示创建结果（增强版）
     * @param {array} results - 创建结果数组
     */
    displayCreateResults(results) {
        const resultsDiv = document.getElementById('createResults');
        const statusSection = document.getElementById('orderStatus');

        if (!resultsDiv) return;

        const successCount = results.filter(r => r.success).length;
        const recoveredCount = results.filter(r => r.success && r.recovered).length;
        
        let html = '<h3>📋 订单创建结果</h3>';
        
        // 总体统计
        html += `<div class="results-summary">`;
        html += `<div class="summary-stat success">✅ 成功: ${successCount}/${results.length}</div>`;
        if (recoveredCount > 0) {
            html += `<div class="summary-stat recovered">🔄 自动恢复: ${recoveredCount}</div>`;
        }
        const failedCount = results.length - successCount;
        if (failedCount > 0) {
            html += `<div class="summary-stat failed">❌ 失败: ${failedCount}</div>`;
        }
        html += `</div>`;

        // 详细结果
        results.forEach((result, index) => {
            html += `<div class="create-result ${result.success ? 'success' : 'error'}">`;
            html += `<div class="result-header">`;
            html += `<h4>订单 ${index + 1}</h4>`;
            
            if (result.success) {
                if (result.recovered) {
                    html += `<span class="status-badge recovered">🔄 自动恢复</span>`;
                } else {
                    html += `<span class="status-badge success">✅ 创建成功</span>`;
                }
            } else {
                html += `<span class="status-badge error">❌ 创建失败</span>`;
                if (result.errorAnalysis?.recoverable) {
                    html += `<span class="status-badge recoverable">🔧 可恢复</span>`;
                }
            }
            html += `</div>`;
            
            // 结果详情
            html += `<div class="result-details">`;
            if (result.success) {
                html += `<p><strong>订单ID:</strong> ${result.result?.id || '未知'}</p>`;
                if (result.orderData?.customerName) {
                    html += `<p><strong>客人:</strong> ${result.orderData.customerName}</p>`;
                }
                if (result.recovered) {
                    html += `<p class="recovery-info">⚡ 系统自动检测到数据问题并成功恢复</p>`;
                }
            } else {
                html += `<p class="error-message"><strong>错误:</strong> ${result.error}</p>`;
                
                // 错误分析信息
                if (result.errorAnalysis) {
                    html += `<div class="error-analysis">`;
                    html += `<p><strong>错误类型:</strong> ${this.getErrorTypeDisplay(result.errorAnalysis.type)}</p>`;
                    html += `<p><strong>严重程度:</strong> ${this.getSeverityDisplay(result.errorAnalysis.severity)}</p>`;
                    
                    if (result.errorAnalysis.recoverable) {
                        html += `<div class="recovery-suggestion">`;
                        html += `<p>💡 <strong>建议:</strong> ${this.getRecoverySuggestion(result.errorAnalysis)}</p>`;
                        html += `</div>`;
                    }
                    html += `</div>`;
                }
            }
            html += `</div>`;
            html += `</div>`;
        });

        // 添加数据一致性状态
        const cacheInfo = this.appState.getCacheInfo();
        html += `<div class="cache-status">`;
        html += `<h4>📊 数据状态</h4>`;
        html += `<p><strong>用户标识:</strong> ${cacheInfo.currentUserHash ? cacheInfo.currentUserHash.substring(0, 8) + '...' : '未设置'}</p>`;
        html += `<p><strong>缓存用户数:</strong> ${cacheInfo.userCount}</p>`;
        if (cacheInfo.hasLegacyData) {
            html += `<p class="warning">⚠️ 检测到旧版本数据，建议重新加载</p>`;
        }
        html += `</div>`;

        resultsDiv.innerHTML = html;

        // 显示订单状态区域
        if (statusSection) {
            statusSection.classList.remove('hidden');
        }

        // 使用通知系统显示结果
        if (window.notificationManager) {
            window.notificationManager.showOrderResults(results);
        }

        logger.debug('UI', '显示创建结果', { 
            successCount, 
            recoveredCount,
            totalCount: results.length 
        });
    }

    /**
     * @function getErrorTypeDisplay - 获取错误类型显示文本
     */
    getErrorTypeDisplay(type) {
        const types = {
            'id_mismatch': 'ID不匹配',
            'auth_error': '认证错误', 
            'validation_error': '数据验证错误',
            'unknown': '未知错误'
        };
        return types[type] || type;
    }

    /**
     * @function getSeverityDisplay - 获取严重程度显示文本
     */
    getSeverityDisplay(severity) {
        const severities = {
            'low': '🟢 低',
            'medium': '🟡 中等',
            'high': '🟠 高',
            'critical': '🔴 严重'
        };
        return severities[severity] || severity;
    }

    /**
     * @function getRecoverySuggestion - 获取恢复建议
     */
    getRecoverySuggestion(errorAnalysis) {
        const suggestions = {
            'refresh_data': '系统将自动刷新数据并重试创建',
            'reauth': '需要重新登录验证身份',
            'manual_check': '请手动检查订单信息是否正确'
        };
        return suggestions[errorAnalysis.suggestedAction] || '请联系技术支持';
    }

    /**
     * @function displayImageResults - 显示图片结果
     * @param {object} results - 图片处理结果
     */
    displayImageResults(results) {
        const resultsDiv = document.getElementById('imageResults');
        if (!resultsDiv) return;

        let html = '<h3>图片处理结果</h3>';
        html += `<p>成功处理 ${results.processedFiles}/${results.totalFiles} 个文件</p>`;

        if (results.results && results.results.length > 0) {
            results.results.forEach((result, index) => {
                html += `<div class="image-result">`;
                html += `<h4>${result.fileName}</h4>`;
                if (result.success) {
                    html += `<p>✅ 处理成功</p>`;
                    html += `<p>识别文字长度: ${result.ocrResult?.text?.length || 0} 字符</p>`;
                } else {
                    html += `<p>❌ 处理失败: ${result.error}</p>`;
                }
                html += `</div>`;
            });
        }

        resultsDiv.innerHTML = html;
        logger.debug('UI', '显示图片结果', { processedFiles: results.processedFiles });
    }

    /**
     * @function initializeFileUpload - 初始化文件上传
     */
    initializeFileUpload() {
        const dropZone = document.getElementById('uploadArea');
        if (dropZone) {
            dropZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            dropZone.addEventListener('drop', (e) => this.handleFileDrop(e));
        }
        logger.debug('UI', '文件上传初始化完成');
    }

    /**
     * @function handleDragOver - 处理拖拽悬停
     * @param {Event} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'copy';
    }

    // 手动编辑相关函数
    /**
     * @function initializeManualEdit - 初始化手动编辑区域
     * @param {object} results - 处理结果
     */
    initializeManualEdit(results) {
        logger.info('应用', '初始化手动编辑区域');

        // 清空现有表单
        const formsContainer = document.getElementById('orderEditForms');
        if (formsContainer) {
            formsContainer.innerHTML = '';
        }

        // 如果有部分识别的信息，创建预填表单
        if (results.metadata && results.metadata.originalText) {
            this.createOrderEditForm(null, results.metadata.originalText);
        } else {
            // 创建空白表单
            this.createOrderEditForm();
        }
    }

    /**
     * @function handleAddOrder - 处理添加新订单
     */
    handleAddOrder() {
        logger.info('应用', '添加新订单表单');
        this.createOrderEditForm();
    }

    /**
     * @function handleReAnalyze - 处理重新分析
     */
    async handleReAnalyze() {
        logger.info('应用', '重新分析订单');

        const textInput = document.getElementById('orderText').value.trim();
        if (!textInput) {
            this.showError('没有订单文本可以重新分析');
            return;
        }

        // 重新处理订单
        await this.handleProcessOrder();
    }

    /**
     * @function createOrderEditForm - 创建订单编辑表单
     * @param {object} orderData - 预填订单数据（可选）
     * @param {string} originalText - 原始文本（可选）
     */
    createOrderEditForm(orderData = null, originalText = '') {
        const formsContainer = document.getElementById('orderEditForms');
        if (!formsContainer) return;

        const formId = `orderForm_${Date.now()}`;
        const formHtml = `
            <div class="order-edit-form" id="${formId}">
                <div class="form-header">
                    <h4>订单信息</h4>
                    <button type="button" class="remove-form-btn" onclick="app.removeOrderForm('${formId}')">删除</button>
                </div>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="${formId}_customerName">客人姓名 *</label>
                        <input type="text" id="${formId}_customerName" value="${orderData?.customerName || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_customerContact">联系方式</label>
                        <input type="text" id="${formId}_customerContact" value="${orderData?.customerContact || ''}">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_customerEmail">客人邮箱</label>
                        <input type="email" id="${formId}_customerEmail" value="${orderData?.customerEmail || ''}" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_flightInfo">航班信息</label>
                        <input type="text" id="${formId}_flightInfo" value="${orderData?.flightInfo || ''}" placeholder="如: CZ8301">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_serviceType">服务类型</label>
                        <select id="${formId}_serviceType">
                            <option value="">选择服务类型</option>
                            <option value="pickup" ${orderData?.serviceType === 'pickup' ? 'selected' : ''}>接机</option>
                            <option value="dropoff" ${orderData?.serviceType === 'dropoff' ? 'selected' : ''}>送机</option>
                            <option value="charter" ${orderData?.serviceType === 'charter' ? 'selected' : ''}>包车</option>
                            <option value="transfer" ${orderData?.serviceType === 'transfer' ? 'selected' : ''}>点对点</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_pickup">上车地点</label>
                        <div class="address-search-container">
                            <div class="address-input-wrapper">
                                <input type="text" 
                                       class="address-input" 
                                       id="${formId}_pickup" 
                                       value="${orderData?.pickup || ''}" 
                                       placeholder="输入地址或点击搜索..."
                                       data-address-type="pickup"
                                       data-form-id="${formId}">
                                <div class="address-search-icon">📍</div>
                                <div class="address-status-indicator"></div>
                                <button type="button" class="clear-address-btn">✕</button>
                            </div>
                            <div class="address-suggestions"></div>
                            <div class="coordinates-display">
                                <span class="coordinates-text">坐标: 等待选择地址...</span>
                            </div>
                            <div class="address-search-help">
                                <span class="help-icon">💡</span>输入地址关键词进行搜索，选择精确位置获取坐标
                            </div>
                        </div>
                        <!-- 隐藏的坐标字段 -->
                        <input type="hidden" id="${formId}_pickup_lat" value="${orderData?.pickup_lat || ''}">
                        <input type="hidden" id="${formId}_pickup_long" value="${orderData?.pickup_long || ''}">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_destination">目的地</label>
                        <div class="address-search-container">
                            <div class="address-input-wrapper">
                                <input type="text" 
                                       class="address-input" 
                                       id="${formId}_destination" 
                                       value="${orderData?.destination || ''}" 
                                       placeholder="输入地址或点击搜索..."
                                       data-address-type="destination"
                                       data-form-id="${formId}">
                                <div class="address-search-icon">📍</div>
                                <div class="address-status-indicator"></div>
                                <button type="button" class="clear-address-btn">✕</button>
                            </div>
                            <div class="address-suggestions"></div>
                            <div class="coordinates-display">
                                <span class="coordinates-text">坐标: 等待选择地址...</span>
                            </div>
                            <div class="address-search-help">
                                <span class="help-icon">💡</span>输入地址关键词进行搜索，选择精确位置获取坐标
                            </div>
                        </div>
                        <!-- 隐藏的坐标字段 -->
                        <input type="hidden" id="${formId}_destination_lat" value="${orderData?.destination_lat || ''}">
                        <input type="hidden" id="${formId}_destination_long" value="${orderData?.destination_long || ''}">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_date">服务日期 *</label>
                        <input type="date" id="${formId}_date" value="${this.formatDateForInput(orderData?.date)}" required>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_time">服务时间 *</label>
                        <input type="time" id="${formId}_time" value="${orderData?.time || ''}" required>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_passengerNumber">乘客人数</label>
                        <input type="number" id="${formId}_passengerNumber" value="${orderData?.passengerNumber || 1}" min="1" max="20">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_luggageNumber">行李件数</label>
                        <input type="number" id="${formId}_luggageNumber" value="${orderData?.luggageNumber || 0}" min="0" max="50">
                    </div>
                    <div class="form-group">
                        <label for="${formId}_meetAndGreet">举牌服务</label>
                        <select id="${formId}_meetAndGreet">
                            <option value="false">否</option>
                            <option value="true" ${orderData?.meetAndGreet ? 'selected' : ''}>是</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_tourGuide">导游服务</label>
                        <select id="${formId}_tourGuide">
                            <option value="false">否</option>
                            <option value="true" ${orderData?.tourGuide ? 'selected' : ''}>是</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="${formId}_babyChair">婴儿座椅</label>
                        <select id="${formId}_babyChair">
                            <option value="false">否</option>
                            <option value="true" ${orderData?.babyChair ? 'selected' : ''}>是</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="${formId}_extraRequirement">额外要求</label>
                        <textarea id="${formId}_extraRequirement" rows="2" placeholder="其他特殊要求...">${orderData?.extraRequirement || ''}</textarea>
                    </div>
                </div>
            </div>
        `;

        formsContainer.insertAdjacentHTML('beforeend', formHtml);
        
        // 添加实时预览功能
        this.setupRealTimePreview(formId);

        // 初始化地址搜索功能
        this.initializeAddressSearch(formId);

        logger.debug('应用', '创建订单编辑表单', { formId, hasPreData: !!orderData });
    }

    /**
     * @function setupRealTimePreview - 设置实时预览
     * @param {string} formId - 表单ID
     */
    setupRealTimePreview(formId) {
        const form = document.getElementById(formId);
        if (!form) return;
        
        // 获取所有输入字段
        const inputs = form.querySelectorAll('input, select, textarea');
        
        // 为每个输入字段添加事件监听器
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.validateField(input);
                this.updateLivePreview();
            });
            input.addEventListener('change', () => {
                this.validateField(input);
                this.updateLivePreview();
            });
            input.addEventListener('blur', () => {
                this.validateField(input);
            });
        });
        
        // 初始化预览
        this.updateLivePreview();
    }

    /**
     * @function validateField - 验证单个字段
     * @param {HTMLElement} field - 输入字段
     */
    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.name;
        let isValid = true;
        let errorMessage = '';
        
        // 移除之前的错误样式
        field.classList.remove('error');
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        
        // 验证必填字段
        if (fieldName === 'customerName' && !value) {
            isValid = false;
            errorMessage = '客人姓名不能为空';
        } else if (fieldName === 'customerEmail' && value) {
            // 验证邮箱格式
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = '请输入有效的邮箱地址';
            }
        } else if (fieldName === 'serviceType' && !value) {
            isValid = false;
            errorMessage = '请选择服务类型';
        } else if (fieldName === 'serviceDate' && value) {
            // 验证日期格式
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(value)) {
                isValid = false;
                errorMessage = '请输入正确的日期格式';
            }
        } else if (fieldName === 'serviceTime' && value) {
            // 验证时间格式
            const timeRegex = /^\d{2}:\d{2}$/;
            if (!timeRegex.test(value)) {
                isValid = false;
                errorMessage = '请输入正确的时间格式 (HH:MM)';
            }
        } else if (fieldName === 'passengerCount' && value) {
            // 验证乘客数量
            const count = parseInt(value);
            if (isNaN(count) || count < 1 || count > 20) {
                isValid = false;
                errorMessage = '乘客数量应在1-20之间';
            }
        } else if (fieldName === 'luggageNumber' && value) {
            // 验证行李件数
            const count = parseInt(value);
            if (isNaN(count) || count < 0 || count > 50) {
                isValid = false;
                errorMessage = '行李件数应在0-50之间';
            }
        }
        
        // 显示错误信息
        if (!isValid) {
            field.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'field-error';
            errorDiv.textContent = errorMessage;
            field.parentNode.appendChild(errorDiv);
        }
        
        return isValid;
    }

    /**
     * @function updateLivePreview - 更新实时预览
     */
    updateLivePreview() {
        const orders = this.collectOrdersFromForms();
        
        // 创建预览结果对象
        const previewResult = {
            success: true,
            orders: orders,
            otaType: 'manual_edit',
            processingTime: 0
        };
        
        // 更新预览显示
        this.displayLivePreview(previewResult);
        
        // 更新保存按钮状态
        this.updateSaveButtonState(orders);
    }

    /**
     * @function updateSaveButtonState - 更新保存按钮状态
     * @param {Array} orders - 订单数组
     */
    updateSaveButtonState(orders) {
        const saveBtn = document.getElementById('saveOrdersBtn');
        if (!saveBtn) return;
        
        const hasValidOrders = orders && orders.length > 0 && orders.some(order => 
            order.customer_name && order.customer_name.trim() !== ''
        );
        
        if (hasValidOrders) {
            saveBtn.disabled = false;
            saveBtn.textContent = `保存订单 (${orders.length})`;
            saveBtn.classList.remove('disabled');
        } else {
            saveBtn.disabled = true;
            saveBtn.textContent = '保存订单';
            saveBtn.classList.add('disabled');
        }
    }

    /**
     * @function displayLivePreview - 显示实时预览
     * @param {object} results - 预览结果
     */
    displayLivePreview(results) {
        const previewDiv = document.getElementById('livePreview');
        
        // 如果预览容器不存在，创建它
        if (!previewDiv) {
            this.createLivePreviewContainer();
            return this.displayLivePreview(results);
        }
        
        if (!results.orders || results.orders.length === 0) {
            previewDiv.innerHTML = '<div class="preview-empty">请填写订单信息以查看预览</div>';
            return;
        }
        
        let html = '<h4>实时预览</h4>';
        html += `<p class="preview-info">共 ${results.orders.length} 个订单</p>`;
        
        results.orders.forEach((order, index) => {
            // 转换字段名以适配显示
            const displayOrder = {
                customerName: order.customer_name || '未填写',
                serviceType: this.getServiceTypeDisplay(order.service_type),
                serviceDate: order.service_date || '未填写',
                serviceTime: order.service_time || '未填写',
                flightNumber: order.flight_info || '未填写',
                passengerCount: order.passenger_number || 1,
                pickupLocation: order.pickup || '未填写',
                dropLocation: order.destination || '未填写',
                extraRequirement: order.extra_requirement || '无'
            };
            
            html += `<div class="preview-order">`;
            html += `<h5>订单 ${index + 1}</h5>`;
            html += `<div class="preview-grid">`;
            html += `<span><strong>客人姓名:</strong> ${displayOrder.customerName}</span>`;
            html += `<span><strong>服务类型:</strong> ${displayOrder.serviceType}</span>`;
            html += `<span><strong>服务日期:</strong> ${displayOrder.serviceDate}</span>`;
            html += `<span><strong>服务时间:</strong> ${displayOrder.serviceTime}</span>`;
            html += `<span><strong>航班信息:</strong> ${displayOrder.flightNumber}</span>`;
            html += `<span><strong>乘客人数:</strong> ${displayOrder.passengerCount}</span>`;
            html += `<span><strong>上车地点:</strong> ${displayOrder.pickupLocation}</span>`;
            html += `<span><strong>下车地点:</strong> ${displayOrder.dropLocation}</span>`;
            if (displayOrder.extraRequirement && displayOrder.extraRequirement !== '无') {
                html += `<span class="full-width"><strong>额外要求:</strong> ${displayOrder.extraRequirement}</span>`;
            }
            html += `</div></div>`;
        });
        
        previewDiv.innerHTML = html;
    }

    /**
     * @function createLivePreviewContainer - 创建实时预览容器
     */
    createLivePreviewContainer() {
        const manualEditSection = document.getElementById('manualEditSection');
        if (!manualEditSection) return;
        
        const previewHtml = `
            <div id="livePreview" class="live-preview">
                <div class="preview-empty">请填写订单信息以查看预览</div>
            </div>
        `;
        
        // 在表单容器之前插入预览容器
        const formsContainer = document.getElementById('orderEditForms');
        if (formsContainer) {
            formsContainer.insertAdjacentHTML('beforebegin', previewHtml);
        }
    }

    /**
     * @function getServiceTypeDisplay - 获取服务类型显示名称
     * @param {string} serviceType - 服务类型
     * @returns {string} 显示名称
     */
    getServiceTypeDisplay(serviceType) {
        const typeMapping = {
            'pickup': '接机',
            'dropoff': '送机',
            'charter': '包车',
            'transfer': '点对点'
        };
        
        return typeMapping[serviceType] || serviceType || '未选择';
    }

    /**
     * @function removeOrderForm - 删除订单表单
     * @param {string} formId - 表单ID
     */
    removeOrderForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.remove();
            logger.debug('应用', '删除订单表单', { formId });
        }
    }

    /**
     * @function collectAllOrders - 收集所有订单数据
     * @returns {Promise<array>} 所有订单数据
     */
    async collectAllOrders() {
        const allOrders = [];

        // 添加已处理的订单（确保包含必需字段）
        if (this.appState.processedOrders && this.appState.processedOrders.length > 0) {
            const processedOrders = await Promise.all(
                this.appState.processedOrders.map(async order => {
                    // 确保每个订单都有必需的API字段
                    return await this.ensureRequiredApiFields(order);
                })
            );
            allOrders.push(...processedOrders);
        }

        // 添加手动编辑的订单
        const manualOrders = await this.collectManualOrders();
        if (manualOrders.length > 0) {
            allOrders.push(...manualOrders);
        }

        return allOrders;
    }

    /**
     * @function ensureRequiredApiFields - 确保订单包含API必需字段
     * @param {object} order - 原始订单数据
     * @returns {Promise<object>} 包含必需字段的订单数据
     */
    async ensureRequiredApiFields(order) {
        // 创建订单副本，避免修改原始数据
        const enhancedOrder = { ...order };

        // 确保包含必需的API字段
        if (!enhancedOrder.ota_reference_number) {
            // 尝试从订单数据中获取OTA类型
            const otaType = enhancedOrder.ota || 'default';
            enhancedOrder.ota_reference_number = await this.generateOTAReference(otaType, enhancedOrder);
        }

        // 如果订单已经通过智能选择增强，保留这些字段
        // 否则，确保有默认值（这些将在API调用时被智能选择覆盖）
        if (!enhancedOrder.sub_category_id) {
            enhancedOrder.sub_category_id = this.getSelectedSubCategoryId() || 7; // 默认Pickup
        }

        if (!enhancedOrder.car_type_id) {
            enhancedOrder.car_type_id = this.getSelectedCarTypeId() || 6; // 默认Comfort 5 Seater
        }

        if (!enhancedOrder.incharge_by_backend_user_id) {
            enhancedOrder.incharge_by_backend_user_id = this.getSelectedBackendUserId() || 1; // 默认Super Admin
        }

        // 确保有OTA类型
        if (!enhancedOrder.ota) {
            enhancedOrder.ota = 'chong-dealer';
        }

        logger.debug('订单', '已确保订单包含必需的API字段', {
            orderId: enhancedOrder.id,
            hasOtaReference: !!enhancedOrder.ota_reference_number,
            hasSubCategory: !!enhancedOrder.sub_category_id,
            hasCarType: !!enhancedOrder.car_type_id,
            hasBackendUser: !!enhancedOrder.incharge_by_backend_user_id
        });

        return enhancedOrder;
    }

    /**
     * @function collectManualOrders - 收集手动编辑的订单
     * @returns {Promise<array>} 手动编辑的订单数据
     */
    async collectManualOrders() {
        const orders = [];
        const forms = document.querySelectorAll('.order-edit-form');

        for (const form of forms) {
            const formId = form.id;
            const orderData = await this.extractOrderDataFromForm(formId);

            if (orderData && this.isValidOrderData(orderData)) {
                orders.push(orderData);
            }
        }

        return orders;
    }

    /**
     * @function extractOrderDataFromForm - 从表单提取订单数据
     * @param {string} formId - 表单ID
     * @returns {Promise<object>} 订单数据
     */
    async extractOrderDataFromForm(formId) {
        const getValue = (fieldName) => {
            const element = document.getElementById(`${formId}_${fieldName}`);
            return element ? element.value.trim() : '';
        };

        const getBooleanValue = (fieldName) => {
            const element = document.getElementById(`${formId}_${fieldName}`);
            return element ? element.value === 'true' : false;
        };

        const getNumberValue = (fieldName) => {
            const element = document.getElementById(`${formId}_${fieldName}`);
            return element ? parseInt(element.value) || 1 : 1;
        };

        // 构建完整的 GoMyHire API 订单数据对象
        let orderData = {
            // 必填字段
            sub_category_id: this.getSelectedSubCategoryId(),
            car_type_id: this.getSelectedCarTypeId(),
            incharge_by_backend_user_id: this.getSelectedBackendUserId(),
            
            // 可选字段 - 客户信息
            customer_name: getValue('customerName'),
            customer_contact: getValue('customerContact'),
            customer_email: getValue('customerEmail') || '<EMAIL>', // 默认邮箱
            
            // 可选字段 - 行程信息
            flight_info: getValue('flightInfo'),
            pickup: getValue('pickup'),
            pickup_lat: getValue('pickup_lat') || null,
            pickup_long: getValue('pickup_long') || null,
            destination: getValue('destination'),
            destination_lat: getValue('destination_lat') || null,
            destination_long: getValue('destination_long') || null,
            service_date: this.formatDateForAPI(getValue('date')),
            service_time: getValue('time'),
            
            // 可选字段 - 服务详情
            passenger_number: getNumberValue('passengerNumber'),
            luggage_number: getNumberValue('luggageNumber') || 0,
            driver_fee: getValue('driverFee') || null,
            driver_collect: getValue('driverCollect') || null,
            tour_guide: getBooleanValue('tourGuide'),
            baby_chair: getBooleanValue('babyChair'),
            meet_and_greet: getBooleanValue('meetAndGreet'),
            extra_requirement: getValue('extraRequirement'),
            
            // 可选字段 - OTA 信息
            ota: getValue('ota') || 'chong-dealer', // 默认 OTA 类型
            ota_price: getValue('otaPrice') || null
        };
        
        // 应用智能选择（如果启用且服务已初始化）
        if (window.smartSelection && window.smartSelection.initialized) {
            try {
                const enhancedData = window.smartSelection.applySmartSelection(orderData, 'manual');
                orderData = { ...orderData, ...enhancedData };
                
                // 更新UI选择器显示智能选择的结果
                window.smartSelection.updateUISelectors(enhancedData);
                
                // 显示智能选择结果给用户
                if (enhancedData._smartSelection) {
                    const smartResults = enhancedData._smartSelection.results;
                    let smartSelectionInfo = '<div class="smart-selection-info"><h4>🤖 智能选择结果:</h4><ul>';
                    
                    if (smartResults.vehicle && smartResults.vehicle.success) {
                        smartSelectionInfo += `<li><strong>车型:</strong> ${smartResults.vehicle.vehicle.name} (置信度: ${(smartResults.vehicle.confidence * 100).toFixed(0)}%) - ${smartResults.vehicle.reason}</li>`;
                    }
                    
                    if (smartResults.service && smartResults.service.success) {
                        smartSelectionInfo += `<li><strong>服务类型:</strong> ${smartResults.service.service.name} (置信度: ${(smartResults.service.confidence * 100).toFixed(0)}%) - ${smartResults.service.reason}</li>`;
                    }
                    
                    if (smartResults.user && smartResults.user.success) {
                        smartSelectionInfo += `<li><strong>负责用户:</strong> ${smartResults.user.user.name} (置信度: ${(smartResults.user.confidence * 100).toFixed(0)}%) - ${smartResults.user.reason}</li>`;
                    }
                    
                    smartSelectionInfo += '</ul></div>';
                    
                    // 将智能选择信息添加到订单数据中，用于显示
                    orderData._smartSelectionDisplay = smartSelectionInfo;
                }
                
                logger.debug('应用', '智能选择已应用到订单数据', {
                    formId: formId,
                    smartSelectionApplied: true,
                    enhancedFields: {
                        car_type_id: enhancedData.car_type_id,
                        sub_category_id: enhancedData.sub_category_id,
                        incharge_by_backend_user_id: enhancedData.incharge_by_backend_user_id
                    }
                });
            } catch (error) {
                logger.error('应用', '智能选择应用失败', error);
            }
        }

        // 生成OTA参考号（在所有数据准备完成后）
        const otaType = orderData.ota || 'default';
        orderData.ota_reference_number = await this.generateOTAReference(otaType, orderData);

        // 清理空值字段（除了必填字段）
        const requiredFields = ['sub_category_id', 'car_type_id', 'incharge_by_backend_user_id', 'ota_reference_number'];
        Object.keys(orderData).forEach(key => {
            if (!requiredFields.includes(key) && (orderData[key] === '' || orderData[key] === null)) {
                delete orderData[key];
            }
        });

        return orderData;
    }

    /**
     * @function isValidOrderData - 验证订单数据是否有效
     * @param {object} orderData - 订单数据
     * @returns {boolean} 是否有效
     */
    isValidOrderData(orderData) {
        // 基本必填字段检查
        return orderData.customer_name &&
               orderData.service_date &&
               orderData.service_time;
    }

    /**
     * @function validateOrderData - 验证订单数据
     * @param {object} orderData - 订单数据
     * @returns {object} 验证结果
     */
    validateOrderData(orderData) {
        const errors = [];

        if (!orderData.customer_name) {
            errors.push('客人姓名不能为空');
        }

        if (!orderData.service_date) {
            errors.push('服务日期不能为空');
        }

        if (!orderData.service_time) {
            errors.push('服务时间不能为空');
        }

        if (!orderData.sub_category_id) {
            errors.push('请选择服务分类');
        }

        if (!orderData.car_type_id) {
            errors.push('请选择车型');
        }

        if (!orderData.incharge_by_backend_user_id) {
            errors.push('请选择负责用户');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * @function formatDateForInput - 格式化日期用于输入框
     * @param {string} dateStr - 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDateForInput(dateStr) {
        if (!dateStr) return '';

        // 如果已经是 YYYY-MM-DD 格式，直接返回
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            return dateStr;
        }

        // 尝试解析其他格式
        try {
            const date = new Date(dateStr);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
        } catch (error) {
            logger.warn('应用', '日期格式化失败', { dateStr, error: error.message });
        }

        return '';
    }

    /**
     * @function formatDateForAPI - 格式化日期用于API调用
     * @param {string} dateStr - 日期字符串 (YYYY-MM-DD)
     * @returns {string} DD-MM-YYYY 格式的日期
     */
    formatDateForAPI(dateStr) {
        if (!dateStr) return '';

        try {
            const [year, month, day] = dateStr.split('-');
            return `${day}-${month}-${year}`;
        } catch (error) {
            logger.warn('应用', 'API日期格式化失败', { dateStr, error: error.message });
            return dateStr;
        }
    }

    /**
     * @function getSelectedSubCategoryId - 获取选中的子分类ID
     * @returns {number} 子分类ID
     */
    getSelectedSubCategoryId() {
        const selector = document.getElementById('subCategorySelect');
        return selector ? parseInt(selector.value) || null : null;
    }

    /**
     * @function getSelectedCarTypeId - 获取选中的车型ID
     * @returns {number} 车型ID
     */
    getSelectedCarTypeId() {
        const selector = document.getElementById('carTypeSelect');
        return selector ? parseInt(selector.value) || null : null;
    }

    /**
     * @function getSelectedBackendUserId - 获取选中的后台用户ID
     * @returns {number} 后台用户ID
     */
    getSelectedBackendUserId() {
        const selector = document.getElementById('backendUserSelect');
        return selector ? parseInt(selector.value) || null : null;
    }

    /**
     * @function generateOTAReference - 生成OTA参考号
     * @param {string} otaType - OTA类型
     * @param {object} orderData - 订单数据（可选）
     * @returns {Promise<string>} OTA参考号
     */
    async generateOTAReference(otaType = 'default', orderData = null) {
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');

        let otaReference;

        switch (otaType.toLowerCase()) {
            case 'chong-dealer':
            case 'chong_dealer':
            case 'chong':
                otaReference = this.generateChongDealerReference(dateStr, timeStr, orderData);
                break;

            case 'fallback':
            case 'universal':
                otaReference = await this.generateFallbackReference(dateStr, timeStr, orderData);
                break;

            default:
                otaReference = this.generateDefaultReference(dateStr, timeStr);
                break;
        }

        return otaReference;
    }

    /**
     * @function generateChongDealerReference - 生成Chong Dealer专用参考号
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @param {object} orderData - 订单数据
     * @returns {string} Chong Dealer参考号
     */
    generateChongDealerReference(dateStr, timeStr, orderData) {
        let customerInitial = 'X';
        let flightCode = 'XXXX';

        if (orderData) {
            // 提取客人姓名首字母
            if (orderData.customer_name) {
                customerInitial = orderData.customer_name.charAt(0).toUpperCase();
            }

            // 提取航班号
            if (orderData.flight_number) {
                flightCode = orderData.flight_number.replace(/[^A-Z0-9]/gi, '').toUpperCase();
            }
        }

        const randomStr = Math.random().toString(36).substr(2, 2).toUpperCase();
        return `CD${dateStr}${timeStr.substr(0, 4)}${customerInitial}${flightCode}${randomStr}`;
    }

    /**
     * @function generateFallbackReference - 生成通用回退参考号
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @param {object} orderData - 订单数据
     * @returns {Promise<string>} 通用参考号
     */
    async generateFallbackReference(dateStr, timeStr, orderData) {
        logger.debug('订单', '开始生成通用回退参考号', {
            hasOrderData: !!orderData,
            hasOtherField: !!(orderData && orderData.other)
        });

        // 优先使用LLM智能识别
        if (orderData && orderData.other && window.llmService) {
            try {
                logger.info('订单', '尝试使用LLM智能识别订单号', {
                    textLength: orderData.other.length
                });

                const extractionResult = await window.llmService.extractOrderNumber(orderData.other);

                if (extractionResult.success && extractionResult.orderNumber) {
                    // 验证提取的订单号
                    const isValid = this.validateExtractedOrderNumber(extractionResult.orderNumber);

                    if (isValid) {
                        const llmReference = `FB${dateStr}${extractionResult.orderNumber}`;

                        logger.success('订单', 'LLM智能识别订单号成功', {
                            originalOrderNumber: extractionResult.orderNumber,
                            confidence: extractionResult.confidence,
                            reasoning: extractionResult.reasoning,
                            finalReference: llmReference
                        });

                        return llmReference;
                    } else {
                        logger.warn('订单', 'LLM提取的订单号验证失败', {
                            extractedOrderNumber: extractionResult.orderNumber,
                            confidence: extractionResult.confidence
                        });
                    }
                } else {
                    logger.info('订单', 'LLM未能识别有效订单号', {
                        success: extractionResult.success,
                        error: extractionResult.error,
                        reasoning: extractionResult.reasoning
                    });
                }

            } catch (error) {
                logger.error('订单', 'LLM订单号识别异常', {
                    error: error.message
                });
            }
        }

        // 降级到正则表达式提取
        logger.info('订单', '降级到正则表达式提取订单号');

        if (orderData && orderData.other) {
            const regexResult = this.extractOrderNumberWithRegex(orderData.other);
            if (regexResult) {
                const regexReference = `FB${dateStr}${regexResult}`;

                logger.info('订单', '正则表达式提取订单号成功', {
                    originalOrderNumber: regexResult,
                    finalReference: regexReference
                });

                return regexReference;
            }
        }

        // 最后降级到随机生成
        const randomStr = Math.random().toString(36).substr(2, 4).toUpperCase();
        const randomReference = `FB${dateStr}${timeStr.substr(0, 4)}${randomStr}`;

        logger.info('订单', '使用随机生成参考号', {
            finalReference: randomReference
        });

        return randomReference;
    }

    /**
     * @function validateExtractedOrderNumber - 验证提取的订单号
     * @param {string} orderNumber - 提取的订单号
     * @returns {boolean} 验证结果
     */
    validateExtractedOrderNumber(orderNumber) {
        if (!orderNumber || typeof orderNumber !== 'string') {
            return false;
        }

        // 最小长度要求
        if (orderNumber.length < 8) {
            logger.debug('订单', '订单号长度不足', {
                orderNumber: orderNumber,
                length: orderNumber.length
            });
            return false;
        }

        // 排除明显的非订单号内容
        const excludePatterns = [
            /^\d{4}-\d{2}-\d{2}/, // 日期格式
            /^\d{2}:\d{2}/, // 时间格式
            /^[+]?\d{10,15}$/, // 电话号码
            /^[A-Z]{2}\d{3,4}$/, // 航班号格式
            /@/, // 邮箱地址
            /酒店|hotel|airport|机场/i, // 地点相关
            /接机|送机|包车|transfer/i // 业务相关
        ];

        for (const pattern of excludePatterns) {
            if (pattern.test(orderNumber)) {
                logger.debug('订单', '订单号匹配排除模式', {
                    orderNumber: orderNumber,
                    pattern: pattern.toString()
                });
                return false;
            }
        }

        // 验证通过的格式
        const validPatterns = [
            /^[A-Z]{2,4}\d{4,8}$/, // 字母+数字组合
            /^\d{8,16}$/, // 纯数字组合
            /^[A-Z0-9]{8,12}$/, // 字母数字混合
            /^25kk\d{8}$/i // 特殊格式
        ];

        for (const pattern of validPatterns) {
            if (pattern.test(orderNumber)) {
                logger.debug('订单', '订单号验证通过', {
                    orderNumber: orderNumber,
                    pattern: pattern.toString()
                });
                return true;
            }
        }

        logger.debug('订单', '订单号未匹配任何有效格式', {
            orderNumber: orderNumber
        });
        return false;
    }

    /**
     * @function extractOrderNumberWithRegex - 使用正则表达式提取订单号
     * @param {string} text - 订单文本
     * @returns {string|null} 提取的订单号或null
     */
    extractOrderNumberWithRegex(text) {
        logger.debug('订单', '开始正则表达式提取订单号', {
            textLength: text.length
        });

        // 优先级顺序的正则表达式模式
        const patterns = [
            // 明确的订单号标识
            { pattern: /订单号[:：]\s*([A-Z0-9]{8,})/i, priority: 1 },
            { pattern: /order\s*(?:number|id)[:：]\s*([A-Z0-9]{8,})/i, priority: 1 },
            { pattern: /reference[:：]\s*([A-Z0-9]{8,})/i, priority: 1 },

            // 特殊格式
            { pattern: /(25kk\d{8})/i, priority: 2 },

            // 字母+数字组合
            { pattern: /([A-Z]{2,4}\d{4,8})/g, priority: 3 },

            // 纯数字组合（8-16位）
            { pattern: /(\d{8,16})/g, priority: 4 },

            // 字母数字混合
            { pattern: /([A-Z0-9]{8,12})/g, priority: 5 }
        ];

        // 按优先级尝试匹配
        for (const { pattern, priority } of patterns) {
            const matches = text.match(pattern);
            if (matches) {
                for (const match of matches) {
                    const candidate = match.length > 1 ? match : matches[1];
                    if (candidate && this.validateExtractedOrderNumber(candidate)) {
                        logger.info('订单', '正则表达式提取成功', {
                            orderNumber: candidate,
                            priority: priority,
                            pattern: pattern.toString()
                        });
                        return candidate;
                    }
                }
            }
        }

        logger.debug('订单', '正则表达式未能提取有效订单号');
        return null;
    }

    /**
     * @function generateDefaultReference - 生成默认参考号
     * @param {string} dateStr - 日期字符串
     * @param {string} timeStr - 时间字符串
     * @returns {string} 默认参考号
     */
    generateDefaultReference(dateStr, timeStr) {
        const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
        return `OTA${dateStr}${timeStr}${randomStr}`;
    }

    /**
     * @function initializeSmartSelection - 初始化智能选择服务
     */
    initializeSmartSelection() {
        try {
            // 检查智能选择服务是否可用
            if (typeof SmartSelectionService !== 'undefined') {
                // 初始化智能选择服务
                window.smartSelection = new SmartSelectionService();
                window.smartSelection.initialize();
                
                logger.success('应用', '智能选择服务初始化完成');
            } else {
                logger.warn('应用', '智能选择服务未找到，跳过初始化');
            }
        } catch (error) {
            logger.error('应用', '智能选择服务初始化失败', error);
        }
    }
    
    /**
     * @function getCurrentSelections - 获取当前选择器的值
     * @returns {Object} 当前选择器的值
     */
    getCurrentSelections() {
        try {
            const backendUserSelect = document.getElementById('backendUserSelect');
            const subCategorySelect = document.getElementById('subCategorySelect');
            const carTypeSelect = document.getElementById('carTypeSelect');
            
            return {
                backendUser: backendUserSelect ? (backendUserSelect.selectedOptions[0]?.text || '未选择') : '未找到',
                subCategory: subCategorySelect ? (subCategorySelect.selectedOptions[0]?.text || '未选择') : '未找到',
                carType: carTypeSelect ? (carTypeSelect.selectedOptions[0]?.text || '未选择') : '未找到'
            };
        } catch (error) {
            logger.error('应用', '获取当前选择器值失败', error);
            return {
                backendUser: '获取失败',
                subCategory: '获取失败',
                carType: '获取失败'
            };
        }
    }
    
    /**
     * @function generateRandomContact - 生成随机联系方式
     * @returns {string} 随机手机号
     */
    generateRandomContact() {
        // 马来西亚手机号格式: +60 1X-XXX-XXXX
        const prefixes = ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19'];
        const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
        const middle = Math.floor(Math.random() * 900) + 100; // 100-999
        const last = Math.floor(Math.random() * 9000) + 1000; // 1000-9999
        
        return `+60 ${prefix}-${middle}-${last}`;
    }

    /**
     * @function initializeAddressSearch - 初始化地址搜索功能
     * @param {string} formId - 表单ID
     * @description 为指定表单的地址输入字段初始化搜索功能
     */
    initializeAddressSearch(formId) {
        try {
            // 确保地址搜索服务已初始化
            if (typeof AddressSearchService === 'undefined') {
                logger.warn('地址搜索', '地址搜索服务未加载');
                return;
            }

            // 如果还没有全局地址搜索服务实例，创建一个
            if (!window.addressSearchService) {
                window.addressSearchService = new AddressSearchService();
                logger.info('地址搜索', '创建地址搜索服务实例');
            }

            // 获取表单中的地址输入字段
            const pickupField = document.getElementById(`${formId}_pickup`);
            const destinationField = document.getElementById(`${formId}_destination`);

            if (pickupField) {
                this.bindAddressSearchEvents(pickupField, formId, 'pickup');
                logger.debug('地址搜索', '绑定pickup地址搜索事件', { formId });
            }

            if (destinationField) {
                this.bindAddressSearchEvents(destinationField, formId, 'destination');
                logger.debug('地址搜索', '绑定destination地址搜索事件', { formId });
            }

        } catch (error) {
            logger.error('地址搜索', '初始化地址搜索功能失败', error);
        }
    }

    /**
     * @function bindAddressSearchEvents - 绑定地址搜索事件
     * @param {HTMLElement} inputField - 输入字段元素
     * @param {string} formId - 表单ID
     * @param {string} fieldType - 字段类型 (pickup/destination)
     * @description 为单个地址输入字段绑定搜索事件和UI交互
     */
    bindAddressSearchEvents(inputField, formId, fieldType) {
        const container = inputField.closest('.address-search-container');
        if (!container) {
            logger.warn('地址搜索', '未找到地址搜索容器', { formId, fieldType });
            return;
        }

        const suggestionsContainer = container.querySelector('.address-suggestions');
        const coordinatesDisplay = container.querySelector('.coordinates-display');
        const statusIndicator = container.querySelector('.address-status-indicator');
        const clearBtn = container.querySelector('.clear-address-btn');
        const searchIcon = container.querySelector('.address-search-icon');

        // 防抖定时器
        let searchTimeout;

        // 输入事件监听
        inputField.addEventListener('input', (event) => {
            const query = event.target.value.trim();
            
            // 显示/隐藏清除按钮
            if (clearBtn) {
                clearBtn.style.display = query ? 'block' : 'none';
            }

            // 清除之前的搜索定时器
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            if (query.length < 2) {
                this.hideAddressSuggestions(suggestionsContainer);
                this.updateCoordinatesDisplay(coordinatesDisplay, null, 'error', '请输入至少2个字符');
                this.updateStatusIndicator(statusIndicator, 'error');
                return;
            }

            // 设置搜索状态
            this.updateStatusIndicator(statusIndicator, 'loading');
            this.updateCoordinatesDisplay(coordinatesDisplay, null, 'loading', '正在搜索...');

            // 防抖搜索
            searchTimeout = setTimeout(async () => {
                try {
                    const suggestions = await window.addressSearchService.searchPlaces(query);
                    this.displayAddressSuggestions(suggestions, suggestionsContainer, inputField, formId, fieldType);
                    
                    if (suggestions.length > 0) {
                        this.updateStatusIndicator(statusIndicator, 'success');
                        this.updateCoordinatesDisplay(coordinatesDisplay, null, 'success', `找到 ${suggestions.length} 个地址`);
                    } else {
                        this.updateStatusIndicator(statusIndicator, 'error');
                        this.updateCoordinatesDisplay(coordinatesDisplay, null, 'error', '未找到匹配地址');
                    }
                } catch (error) {
                    logger.error('地址搜索', '搜索地址失败', error);
                    this.updateStatusIndicator(statusIndicator, 'error');
                    this.updateCoordinatesDisplay(coordinatesDisplay, null, 'error', '搜索失败，请稍后重试');
                    this.hideAddressSuggestions(suggestionsContainer);
                }
            }, 300); // 300ms防抖
        });

        // 清除按钮事件
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                inputField.value = '';
                this.hideAddressSuggestions(suggestionsContainer);
                this.clearCoordinates(formId, fieldType);
                this.updateCoordinatesDisplay(coordinatesDisplay, null, '', '坐标: 等待选择地址...');
                this.updateStatusIndicator(statusIndicator, '');
                clearBtn.style.display = 'none';
                inputField.focus();
            });
        }

        // 失去焦点事件（延迟隐藏建议，以便可以点击建议项）
        inputField.addEventListener('blur', () => {
            setTimeout(() => {
                this.hideAddressSuggestions(suggestionsContainer);
            }, 200);
        });

        // 获得焦点事件
        inputField.addEventListener('focus', () => {
            if (inputField.value.trim().length >= 2) {
                const event = new Event('input');
                inputField.dispatchEvent(event);
            }
        });

        logger.debug('地址搜索', '地址搜索事件绑定完成', { formId, fieldType });
    }

    /**
     * @function displayAddressSuggestions - 显示地址建议列表
     * @param {Array} suggestions - 地址建议数组
     * @param {HTMLElement} container - 建议容器元素
     * @param {HTMLElement} inputField - 输入字段元素
     * @param {string} formId - 表单ID
     * @param {string} fieldType - 字段类型
     */
    displayAddressSuggestions(suggestions, container, inputField, formId, fieldType) {
        if (!container) return;

        container.innerHTML = '';

        if (!suggestions || suggestions.length === 0) {
            container.innerHTML = '<div class="suggestion-no-results">未找到匹配的地址</div>';
        } else {
            suggestions.forEach((suggestion, index) => {
                const item = document.createElement('div');
                item.className = 'address-suggestion-item';
                item.innerHTML = `
                    <div class="suggestion-main-text">${suggestion.main_text || suggestion.formatted_address}</div>
                    <div class="suggestion-secondary-text">${suggestion.secondary_text || suggestion.place_types?.join(', ') || ''}</div>
                `;

                // 添加地址类型标签
                if (suggestion.place_types) {
                    const typeMapping = {
                        'airport': 'airport',
                        'lodging': 'hotel',
                        'restaurant': 'restaurant'
                    };
                    
                    for (const type of suggestion.place_types) {
                        if (typeMapping[type]) {
                            item.innerHTML += `<span class="address-type-badge ${typeMapping[type]}">${type}</span>`;
                            break;
                        }
                    }
                }

                // 点击事件
                item.addEventListener('click', () => {
                    this.selectAddress(suggestion, inputField, formId, fieldType);
                    this.hideAddressSuggestions(container);
                });

                container.appendChild(item);
            });
        }

        container.classList.add('show');
    }

    /**
     * @function selectAddress - 选择地址
     * @param {Object} suggestion - 地址建议对象
     * @param {HTMLElement} inputField - 输入字段元素
     * @param {string} formId - 表单ID
     * @param {string} fieldType - 字段类型
     */
    async selectAddress(suggestion, inputField, formId, fieldType) {
        try {
            // 设置地址文本
            inputField.value = suggestion.formatted_address || suggestion.main_text;

            // 获取详细地址信息和坐标
            const placeDetails = await window.addressSearchService.getPlaceDetails(suggestion.place_id);
            
            if (placeDetails && placeDetails.geometry) {
                const lat = placeDetails.geometry.location.lat;
                const lng = placeDetails.geometry.location.lng;

                // 更新坐标字段
                this.updateCoordinates(formId, fieldType, lat, lng);

                // 更新坐标显示
                const container = inputField.closest('.address-search-container');
                const coordinatesDisplay = container?.querySelector('.coordinates-display');
                const statusIndicator = container?.querySelector('.address-status-indicator');

                this.updateCoordinatesDisplay(coordinatesDisplay, { lat, lng }, 'success');
                this.updateStatusIndicator(statusIndicator, 'success');

                logger.info('地址搜索', '地址选择成功', {
                    formId,
                    fieldType,
                    address: inputField.value,
                    coordinates: { lat, lng }
                });

                // 触发实时预览更新
                this.updateLivePreview();
            } else {
                throw new Error('无法获取地点坐标');
            }

        } catch (error) {
            logger.error('地址搜索', '选择地址失败', error);
            
            // 更新错误状态
            const container = inputField.closest('.address-search-container');
            const coordinatesDisplay = container?.querySelector('.coordinates-display');
            const statusIndicator = container?.querySelector('.address-status-indicator');

            this.updateCoordinatesDisplay(coordinatesDisplay, null, 'error', '获取坐标失败');
            this.updateStatusIndicator(statusIndicator, 'error');
        }
    }

    /**
     * @function updateCoordinates - 更新坐标字段
     * @param {string} formId - 表单ID
     * @param {string} fieldType - 字段类型
     * @param {number} lat - 纬度
     * @param {number} lng - 经度
     */
    updateCoordinates(formId, fieldType, lat, lng) {
        const latField = document.getElementById(`${formId}_${fieldType}_lat`);
        const lngField = document.getElementById(`${formId}_${fieldType}_long`);

        if (latField) latField.value = lat;
        if (lngField) lngField.value = lng;

        logger.debug('地址搜索', '更新坐标字段', { formId, fieldType, lat, lng });
    }

    /**
     * @function clearCoordinates - 清除坐标字段
     * @param {string} formId - 表单ID
     * @param {string} fieldType - 字段类型
     */
    clearCoordinates(formId, fieldType) {
        const latField = document.getElementById(`${formId}_${fieldType}_lat`);
        const lngField = document.getElementById(`${formId}_${fieldType}_long`);

        if (latField) latField.value = '';
        if (lngField) lngField.value = '';

        logger.debug('地址搜索', '清除坐标字段', { formId, fieldType });
    }

    /**
     * @function updateCoordinatesDisplay - 更新坐标显示
     * @param {HTMLElement} display - 坐标显示元素
     * @param {Object} coordinates - 坐标对象 {lat, lng}
     * @param {string} status - 状态 (success/error/loading)
     * @param {string} message - 显示消息
     */
    updateCoordinatesDisplay(display, coordinates, status, message) {
        if (!display) return;

        let text = '';
        if (coordinates) {
            text = `坐标: ${coordinates.lat.toFixed(6)}, ${coordinates.lng.toFixed(6)}`;
        } else if (message) {
            text = message;
        }

        display.innerHTML = `<span class="coordinates-text">${text}</span>`;
        display.className = `coordinates-display show ${status ? `coordinates-${status}` : ''}`;
    }

    /**
     * @function updateStatusIndicator - 更新状态指示器
     * @param {HTMLElement} indicator - 状态指示器元素
     * @param {string} status - 状态 (success/error/loading)
     */
    updateStatusIndicator(indicator, status) {
        if (!indicator) return;

        indicator.className = `address-status-indicator ${status ? `show ${status}` : ''}`;
    }

    /**
     * @function hideAddressSuggestions - 隐藏地址建议
     * @param {HTMLElement} container - 建议容器元素
     */
    hideAddressSuggestions(container) {
        if (container) {
            container.classList.remove('show');
        }
    }

    /**
     * @function initializeAddressSearchService - 初始化地址搜索服务
     */
    initializeAddressSearchService() {
        try {
            if (window.addressSearchService) {
                const status = window.addressSearchService.getStatus();
                logger.info('地址搜索', '地址搜索服务初始化完成', status);
            } else {
                logger.warn('地址搜索', '地址搜索服务未找到，可能影响地址搜索功能');
            }
        } catch (error) {
            logger.error('地址搜索', '地址搜索服务初始化失败', error);
        }
    }
}

/**
 * @class DataConsistencyManager - 数据一致性管理器
 * @description 负责验证和维护用户数据的一致性
 */
class DataConsistencyManager {
    constructor(appState, apiService) {
        this.appState = appState;
        this.apiService = apiService;
        this.validationRules = {
            maxCacheAge: 24 * 60 * 60 * 1000, // 24小时
            requiredDataKeys: ['backendUsers', 'subCategories', 'carTypes']
        };
    }
    
    /**
     * @function validateUserData - 验证用户数据一致性
     * @returns {Promise<boolean>} 验证结果
     */
    async validateUserData() {
        if (!this.appState.currentUserHash) {
            return false;
        }
        
        try {
            // 1. 检查缓存完整性
            const completenessCheck = this.checkDataCompleteness();
            
            // 2. 检查缓存时效性
            const freshnessCheck = this.checkDataFreshness();
            
            // 3. 检查数据关联性
            const associationCheck = await this.checkDataAssociation();
            
            const isValid = completenessCheck && freshnessCheck && associationCheck;
            
            logger.info('DataConsistency', '数据一致性验证完成', {
                userHash: this.appState.currentUserHash,
                completeness: completenessCheck,
                freshness: freshnessCheck,
                association: associationCheck,
                isValid
            });
            
            return isValid;
        } catch (error) {
            logger.error('DataConsistency', '数据一致性验证失败', error);
            return false;
        }
    }
    
    /**
     * @function checkDataCompleteness - 检查数据完整性
     */
    checkDataCompleteness() {
        return this.validationRules.requiredDataKeys.every(key => {
            const data = this.appState.getUserSystemData(key) || this.appState[key];
            return data && Array.isArray(data) && data.length > 0;
        });
    }
    
    /**
     * @function checkDataFreshness - 检查数据时效性
     */
    checkDataFreshness() {
        const userCache = this.appState.userDataCache.get(this.appState.currentUserHash);
        if (!userCache) return false;
        
        const cacheAge = Date.now() - userCache.cacheTime;
        return cacheAge <= this.validationRules.maxCacheAge;
    }
    
    /**
     * @function checkDataAssociation - 检查数据关联性
     */
    async checkDataAssociation() {
        // 抽样检查：验证后台用户数据是否属于当前账号
        try {
            const currentUsers = await this.apiService.getBackendUsers();
            const cachedUsers = this.appState.getUserSystemData('backendUsers') || this.appState.backendUsers;
            
            if (!cachedUsers || cachedUsers.length === 0) return false;
            
            // 比较用户列表的哈希值（简化的关联性检查）
            const currentHash = this.generateDataHash(currentUsers);
            const cachedHash = this.generateDataHash(cachedUsers);
            
            return currentHash === cachedHash;
        } catch (error) {
            logger.warn('DataConsistency', '数据关联性检查失败', error);
            return false; // 保守策略：检查失败则认为数据不一致
        }
    }
    
    /**
     * @function generateDataHash - 生成数据哈希值
     */
    generateDataHash(data) {
        const sortedData = data.map(item => item.id).sort();
        return btoa(JSON.stringify(sortedData));
    }
    
    /**
     * @function forceDataRefresh - 强制刷新数据
     */
    async forceDataRefresh() {
        logger.info('DataConsistency', '开始强制数据刷新');
        
        // 清理当前缓存
        this.appState.clearUserSpecificData();
        
        // 重新获取数据
        await Promise.all([
            this.apiService.getBackendUsers(),
            this.apiService.getSubCategories(),
            this.apiService.getCarTypes()
        ]);
        
        // 更新智能选择服务
        if (window.smartSelection) {
            await window.smartSelection.updateMappingFromAppState();
        }
        
        logger.success('DataConsistency', '数据刷新完成');
    }
}

/**
 * @class ErrorRecoveryManager - 错误恢复管理器
 * @description 负责分析错误类型并尝试自动恢复
 */
class ErrorRecoveryManager {
    constructor(appState, apiService, dataConsistencyManager) {
        this.appState = appState;
        this.apiService = apiService;
        this.dataConsistencyManager = dataConsistencyManager;
        this.errorPatterns = {
            idNotFound: /id.*not.*found|invalid.*id|does.*not.*exist/i,
            validationError: /validation.*error|field.*required|invalid.*field/i,
            authError: /unauthorized|authentication.*failed|token.*invalid/i
        };
    }
    
    /**
     * @function analyzeError - 分析错误类型
     * @param {Error} error - 错误对象
     * @returns {object} 错误分析结果
     */
    analyzeError(error) {
        const errorMessage = error.message || '';
        const response = error.response?.data || {};
        
        const analysis = {
            type: 'unknown',
            severity: 'medium',
            recoverable: false,
            suggestedAction: 'manual_check'
        };
        
        // ID相关错误检测
        if (this.errorPatterns.idNotFound.test(errorMessage) || 
            response.validation_error) {
            analysis.type = 'id_mismatch';
            analysis.severity = 'high';
            analysis.recoverable = true;
            analysis.suggestedAction = 'refresh_data';
        }
        
        // 认证错误检测
        if (this.errorPatterns.authError.test(errorMessage)) {
            analysis.type = 'auth_error';
            analysis.severity = 'critical';
            analysis.recoverable = true;
            analysis.suggestedAction = 'reauth';
        }
        
        return analysis;
    }
    
    /**
     * @function attemptRecovery - 尝试错误恢复
     * @param {object} errorAnalysis - 错误分析结果
     * @param {object} originalOrderData - 原始订单数据
     * @returns {Promise<object>} 恢复结果
     */
    async attemptRecovery(errorAnalysis, originalOrderData) {
        logger.info('ErrorRecovery', '开始错误恢复', errorAnalysis);
        
        try {
            switch (errorAnalysis.suggestedAction) {
                case 'refresh_data':
                    return await this.recoverFromIdMismatch(originalOrderData);
                
                case 'reauth':
                    return await this.recoverFromAuthError();
                
                default:
                    return { success: false, reason: 'no_recovery_strategy' };
            }
        } catch (error) {
            logger.error('ErrorRecovery', '错误恢复失败', error);
            return { success: false, reason: 'recovery_failed', error };
        }
    }
    
    /**
     * @function recoverFromIdMismatch - 从ID不匹配错误恢复
     */
    async recoverFromIdMismatch(originalOrderData) {
        logger.info('ErrorRecovery', '开始ID不匹配恢复流程');
        
        // 1. 强制刷新数据
        await this.dataConsistencyManager.forceDataRefresh();
        
        // 2. 重新应用智能选择
        if (window.smartSelection) {
            const updatedOrderData = window.smartSelection.applySmartSelection(
                originalOrderData, 
                'error_recovery'
            );
            
            // 3. 重试创建订单
            try {
                const result = await this.apiService.createOrder(updatedOrderData);
                logger.success('ErrorRecovery', 'ID不匹配恢复成功');
                return { success: true, result, orderData: updatedOrderData };
            } catch (retryError) {
                logger.error('ErrorRecovery', 'ID不匹配恢复失败', retryError);
                return { success: false, reason: 'retry_failed', error: retryError };
            }
        }
        
        return { success: false, reason: 'smart_selection_unavailable' };
    }
    
    /**
     * @function recoverFromAuthError - 从认证错误恢复
     */
    async recoverFromAuthError() {
        logger.info('ErrorRecovery', '开始认证错误恢复流程');
        
        // 认证错误通常需要用户重新登录
        this.appState.clearAuth();
        
        return { 
            success: false, 
            reason: 'auth_required',
            userAction: 'relogin'
        };
    }
}

// 全局应用实例
let app;

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', async () => {
    try {
        app = new OTAOrderApp();
        await app.initialize();
    } catch (error) {
        console.error('应用初始化失败:', error);
    }
});

// 导出应用类
if (typeof window !== 'undefined') {
    window.OTAOrderApp = OTAOrderApp;
    window.DataConsistencyManager = DataConsistencyManager;
    window.ErrorRecoveryManager = ErrorRecoveryManager;
}
