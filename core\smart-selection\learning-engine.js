/**
 * @file learning-engine.js - 智能学习引擎
 * @description 从历史数据中学习，自动优化匹配算法和权重
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 */

/**
 * @class IntelligentLearningEngine - 智能学习引擎
 * @description 从历史数据中学习，自动优化匹配算法和权重
 */
class IntelligentLearningEngine {
    constructor() {
        this.learningData = this.loadLearningData();
        this.performanceMetrics = {
            totalSelections: 0,
            successfulSelections: 0,
            failedSelections: 0,
            accuracyRate: 0,
            lastUpdate: Date.now()
        };
        
        // 动态权重调整因子
        this.dynamicWeights = {
            passengerCount: 0.4,
            keywordMatch: 0.3,
            serviceType: 0.2,
            contextual: 0.1
        };
        
        // 学习阈值
        this.learningThreshold = {
            minSamples: 10,          // 最少样本数
            confidenceThreshold: 0.8, // 置信度阈值
            adaptionRate: 0.1        // 自适应学习率
        };
    }
    
    /**
     * @function loadLearningData - 加载学习数据
     * @returns {object} 学习数据
     */
    loadLearningData() {
        try {
            const savedData = localStorage.getItem('smartSelection_learningData');
            return savedData ? JSON.parse(savedData) : {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        } catch (error) {
            console.warn('智能学习引擎: 加载学习数据失败', error);
            return {
                successfulMatches: [],
                failedMatches: [],
                userPreferences: {},
                patternRecognition: {}
            };
        }
    }
    
    /**
     * @function saveLearningData - 保存学习数据
     */
    saveLearningData() {
        try {
            localStorage.setItem('smartSelection_learningData', JSON.stringify(this.learningData));
        } catch (error) {
            console.warn('智能学习引擎: 保存学习数据失败', error);
        }
    }
    
    /**
     * @function recordSelection - 记录选择结果
     * @param {object} selectionRecord - 选择记录
     */
    recordSelection(selectionRecord) {
        const record = {
            ...selectionRecord,
            timestamp: Date.now(),
            sessionId: this.getSessionId()
        };
        
        if (selectionRecord.success) {
            this.learningData.successfulMatches.push(record);
            this.performanceMetrics.successfulSelections++;
        } else {
            this.learningData.failedMatches.push(record);
            this.performanceMetrics.failedSelections++;
        }
        
        this.performanceMetrics.totalSelections++;
        this.updateAccuracyRate();
        
        // 限制记录数量，避免内存过载
        if (this.learningData.successfulMatches.length > 1000) {
            this.learningData.successfulMatches = this.learningData.successfulMatches.slice(-500);
        }
        if (this.learningData.failedMatches.length > 500) {
            this.learningData.failedMatches = this.learningData.failedMatches.slice(-250);
        }
        
        this.saveLearningData();
        this.adaptWeights();
    }
    
    /**
     * @function getSessionId - 获取会话ID
     * @returns {string} 会话ID
     */
    getSessionId() {
        if (!window.sessionStorage.getItem('smartSelection_sessionId')) {
            window.sessionStorage.setItem('smartSelection_sessionId', 
                'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9));
        }
        return window.sessionStorage.getItem('smartSelection_sessionId');
    }
    
    /**
     * @function updateAccuracyRate - 更新准确率
     */
    updateAccuracyRate() {
        if (this.performanceMetrics.totalSelections > 0) {
            this.performanceMetrics.accuracyRate = 
                this.performanceMetrics.successfulSelections / this.performanceMetrics.totalSelections;
        }
        this.performanceMetrics.lastUpdate = Date.now();
    }
    
    /**
     * @function adaptWeights - 自适应权重调整
     */
    adaptWeights() {
        if (this.learningData.successfulMatches.length < this.learningThreshold.minSamples) {
            return; // 样本数不足，不进行调整
        }
        
        // 分析成功匹配的模式
        const recentSuccess = this.learningData.successfulMatches.slice(-50);
        const methodStats = {};
        
        recentSuccess.forEach(record => {
            const method = record.method || 'unknown';
            if (!methodStats[method]) {
                methodStats[method] = { count: 0, totalConfidence: 0 };
            }
            methodStats[method].count++;
            methodStats[method].totalConfidence += (record.confidence || 0.5);
        });
        
        // 根据成功率调整权重
        const totalSamples = recentSuccess.length;
        for (const [method, stats] of Object.entries(methodStats)) {
            const successRate = stats.count / totalSamples;
            const avgConfidence = stats.totalConfidence / stats.count;
            
            // 自适应调整对应的权重
            if (method.includes('passenger') && successRate > 0.8) {
                this.dynamicWeights.passengerCount = Math.min(0.6, 
                    this.dynamicWeights.passengerCount + this.learningThreshold.adaptionRate);
            }
            if (method.includes('keyword') && successRate > 0.8) {
                this.dynamicWeights.keywordMatch = Math.min(0.5, 
                    this.dynamicWeights.keywordMatch + this.learningThreshold.adaptionRate);
            }
        }
        
        // 权重归一化
        const totalWeight = Object.values(this.dynamicWeights).reduce((sum, weight) => sum + weight, 0);
        for (const key of Object.keys(this.dynamicWeights)) {
            this.dynamicWeights[key] = this.dynamicWeights[key] / totalWeight;
        }
    }
    
    /**
     * @function getOptimalWeight - 获取优化后的权重
     * @param {string} method - 方法名称
     * @returns {number} 权重值
     */
    getOptimalWeight(method) {
        const baseWeights = {
            'passenger_count_exact': this.dynamicWeights.passengerCount,
            'enhanced_keyword_match': this.dynamicWeights.keywordMatch,
            'service_mapping': this.dynamicWeights.serviceType,
            'contextual_analysis': this.dynamicWeights.contextual
        };
        
        return baseWeights[method] || 0.5;
    }
    
    /**
     * @function analyzePatterns - 分析匹配模式
     * @param {object} orderData - 订单数据
     * @returns {object} 模式分析结果
     */
    analyzePatterns(orderData) {
        const patterns = {
            suggestedMethod: null,
            confidence: 0,
            reasonCode: '',
            historicalAccuracy: 0
        };
        
        // 查找相似的历史成功案例
        const similarCases = this.findSimilarSuccessfulCases(orderData);
        
        if (similarCases.length > 0) {
            // 统计最常用的成功方法
            const methodCounts = {};
            similarCases.forEach(similarCase => {
                const method = similarCase.method || 'unknown';
                methodCounts[method] = (methodCounts[method] || 0) + 1;
            });
            
            // 找到最佳方法
            const bestMethod = Object.entries(methodCounts)
                .sort(([,a], [,b]) => b - a)[0];
            
            if (bestMethod) {
                patterns.suggestedMethod = bestMethod[0];
                patterns.confidence = bestMethod[1] / similarCases.length;
                patterns.historicalAccuracy = this.calculateMethodAccuracy(bestMethod[0]);
                patterns.reasonCode = `基于${similarCases.length}个相似成功案例的分析`;
            }
        }
        
        return patterns;
    }
    
    /**
     * @function findSimilarSuccessfulCases - 查找相似的成功案例
     * @param {object} orderData - 订单数据
     * @returns {array} 相似案例列表
     */
    findSimilarSuccessfulCases(orderData) {
        const similarCases = [];
        const passengerCount = parseInt(orderData.passenger_count) || 1;
        
        this.learningData.successfulMatches.forEach(record => {
            let similarity = 0;
            
            // 乘客数量相似度
            if (record.orderData && record.orderData.passenger_count) {
                const recordPassengers = parseInt(record.orderData.passenger_count) || 1;
                const passengerDiff = Math.abs(passengerCount - recordPassengers);
                similarity += passengerDiff <= 2 ? 0.4 : 0;
            }
            
            // 服务类型相似度
            if (record.orderData && orderData.service_type) {
                const recordService = (record.orderData.service_type || '').toLowerCase();
                const currentService = (orderData.service_type || '').toLowerCase();
                if (recordService === currentService) similarity += 0.3;
            }
            
            // 关键词相似度
            if (record.orderData) {
                const recordText = (record.orderData.remarks || '').toLowerCase();
                const currentText = (orderData.remarks || '').toLowerCase();
                if (recordText && currentText) {
                    const commonWords = recordText.split(' ').filter(word => 
                        currentText.includes(word) && word.length > 2);
                    similarity += Math.min(0.3, commonWords.length * 0.1);
                }
            }
            
            // 如果相似度足够高，加入结果
            if (similarity > 0.5) {
                similarCases.push({
                    ...record,
                    similarity: similarity
                });
            }
        });
        
        // 按相似度排序并返回最相似的案例
        return similarCases.sort((a, b) => b.similarity - a.similarity).slice(0, 10);
    }
    
    /**
     * @function calculateMethodAccuracy - 计算方法准确率
     * @param {string} method - 方法名称
     * @returns {number} 准确率
     */
    calculateMethodAccuracy(method) {
        const successCases = this.learningData.successfulMatches.filter(
            record => record.method === method);
        const failedCases = this.learningData.failedMatches.filter(
            record => record.method === method);
        
        const total = successCases.length + failedCases.length;
        return total > 0 ? successCases.length / total : 0;
    }
    
    /**
     * @function getPerformanceMetrics - 获取性能指标
     * @returns {object} 性能指标
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            dynamicWeights: { ...this.dynamicWeights },
            dataSize: {
                successfulMatches: this.learningData.successfulMatches.length,
                failedMatches: this.learningData.failedMatches.length
            }
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = IntelligentLearningEngine;
} else if (typeof window !== 'undefined') {
    window.IntelligentLearningEngine = IntelligentLearningEngine;
}
