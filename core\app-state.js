/**
 * @file app-state.js - 应用状态管理模块
 * @description 用户数据管理、缓存机制、数据验证、用户切换检测
 * <AUTHOR> IDE
 * @created_at 2025-01-06
 * @updated_at 2025-01-06
 * @version v4.0.1
 */

/**
 * @class AppState - 应用状态管理类
 * @description 管理应用的全局状态，包括用户认证、数据缓存、用户切换检测
 */
class AppState {
    /**
     * @function constructor - 构造函数
     * @description 初始化应用状态，设置用户数据缓存机制
     */
    constructor() {
        this.token = localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        this.userInfo = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO) || 'null');
        this.currentUserHash = null; // 当前用户标识
        this.userDataCache = new Map(); // 用户数据缓存
        this.backendUsers = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.BACKEND_USERS) || '[]');
        this.subCategories = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.SUB_CATEGORIES) || '[]');
        this.carTypes = JSON.parse(localStorage.getItem(SYSTEM_CONFIG.STORAGE_KEYS.CAR_TYPES) || '[]');
        this.processedOrders = [];
        
        // 初始化用户数据
        this.initializeUserData();
    }

    /**
     * @function initializeUserData - 初始化用户数据
     * @description 设置用户标识和加载用户特定的系统数据
     */
    initializeUserData() {
        if (this.userInfo) {
            this.currentUserHash = this.getUserHash();
            this.loadUserSystemData();
            logger.info('AppState', '用户数据初始化完成', {
                userHash: this.currentUserHash,
                userEmail: this.userInfo.email
            });
        } else {
            logger.debug('AppState', '无用户信息，使用兼容模式');
        }
    }

    /**
     * @function getUserHash - 生成用户标识哈希
     * @description 基于用户邮箱和ID生成唯一标识
     * @returns {string|null} 用户唯一标识
     */
    getUserHash() {
        if (!this.userInfo) return null;
        
        // 基于用户邮箱和ID生成哈希
        const userKey = `${this.userInfo.email || 'unknown'}_${this.userInfo.id || 'anonymous'}`;
        return btoa(userKey).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * @function setToken - 设置认证令牌
     * @description 更新认证令牌并持久化存储
     * @param {string} token - 认证令牌
     */
    setToken(token) {
        this.token = token;
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN, token);
    }

    /**
     * @function setUserInfo - 设置用户信息并更新缓存策略
     * @description 设置用户信息，检测用户切换，管理数据缓存
     * @param {object} userInfo - 用户信息对象
     */
    setUserInfo(userInfo) {
        const oldUserHash = this.currentUserHash;
        const oldUserEmail = this.userInfo?.email;
        
        this.userInfo = userInfo;
        this.currentUserHash = this.getUserHash();
        
        // 检测用户切换
        if (oldUserHash && oldUserHash !== this.currentUserHash) {
            logger.info('AppState', '检测到用户切换', {
                oldUser: oldUserEmail,
                newUser: userInfo?.email,
                oldHash: oldUserHash,
                newHash: this.currentUserHash
            });
            
            // 清理旧用户数据
            this.clearUserSpecificData();
            
            // 触发用户切换事件（供主应用使用）
            this.triggerUserSwitchEvent(oldUserEmail, userInfo?.email);
        }
        
        localStorage.setItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO, JSON.stringify(userInfo));
        
        // 加载新用户的系统数据
        if (this.currentUserHash) {
            this.loadUserSystemData();
        }
    }

    /**
     * @function triggerUserSwitchEvent - 触发用户切换事件
     * @description 发送自定义事件通知应用用户已切换
     * @param {string} oldUser - 旧用户邮箱
     * @param {string} newUser - 新用户邮箱
     */
    triggerUserSwitchEvent(oldUser, newUser) {
        const event = new CustomEvent('userSwitch', {
            detail: { oldUser, newUser }
        });
        window.dispatchEvent(event);
    }

    /**
     * @function clearAuth - 清除认证信息
     * @description 清除所有认证相关的数据和状态
     */
    clearAuth() {
        logger.info('AppState', '清除认证信息', { 
            userHash: this.currentUserHash 
        });
        
        this.token = null;
        this.userInfo = null;
        this.currentUserHash = null;
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.TOKEN);
        localStorage.removeItem(SYSTEM_CONFIG.STORAGE_KEYS.USER_INFO);
    }

    /**
     * @function cacheSystemData - 用户关联的系统数据缓存
     * @description 将系统数据与用户关联并缓存，支持用户数据隔离
     * @param {string} key - 缓存键
     * @param {any} data - 要缓存的数据
     */
    cacheSystemData(key, data) {
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据缓存', { key });
            // 向后兼容：仍然更新实例属性和旧版存储
            this[key] = data;
            const storageKey = SYSTEM_CONFIG.STORAGE_KEYS[key.toUpperCase()];
            if (storageKey) {
                localStorage.setItem(storageKey, JSON.stringify(data));
            }
            return;
        }
        
        // 获取或创建用户缓存空间
        if (!this.userDataCache.has(this.currentUserHash)) {
            this.userDataCache.set(this.currentUserHash, {
                cacheTime: Date.now(),
                data: {}
            });
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        userCache.data[key] = data;
        userCache.lastUpdate = Date.now();
        
        // 持久化存储（添加用户前缀）
        const storageKey = `${this.currentUserHash}_${key}`;
        this.saveToStorage(storageKey, data);
        
        // 更新实例属性（向后兼容）
        this[key] = data;
        
        logger.debug('AppState', '用户数据缓存已更新', {
            userHash: this.currentUserHash,
            key,
            dataSize: data ? JSON.stringify(data).length : 0
        });
    }

    /**
     * @function getUserSystemData - 获取用户系统数据
     * @description 从用户缓存中获取特定的系统数据
     * @param {string} key - 数据键
     * @returns {any} 缓存数据
     */
    getUserSystemData(key) {
        if (!this.currentUserHash) {
            // 向后兼容：返回实例属性
            return this[key] || null;
        }
        
        const userCache = this.userDataCache.get(this.currentUserHash);
        return userCache?.data?.[key] || null;
    }

    /**
     * @function clearUserSpecificData - 清理用户特定数据
     * @description 清理当前用户的所有缓存数据，用于用户切换时
     */
    clearUserSpecificData() {
        logger.info('AppState', '开始清理用户特定数据', { 
            userHash: this.currentUserHash 
        });
        
        // 清理内存缓存
        this.backendUsers = [];
        this.subCategories = [];
        this.carTypes = [];
        this.processedOrders = [];
        
        // 清理当前用户的持久化缓存
        if (this.currentUserHash) {
            const keys = ['backendUsers', 'subCategories', 'carTypes'];
            keys.forEach(key => {
                const storageKey = `${this.currentUserHash}_${key}`;
                localStorage.removeItem(storageKey);
            });
            
            this.userDataCache.delete(this.currentUserHash);
        }
        
        logger.success('AppState', '用户特定数据清理完成');
    }

    /**
     * @function loadUserSystemData - 加载用户系统数据
     * @description 从持久化存储中加载用户特定的系统数据
     */
    loadUserSystemData() {
        if (!this.currentUserHash) {
            logger.warn('AppState', '无用户标识，跳过数据加载');
            return;
        }
        
        const keys = ['backendUsers', 'subCategories', 'carTypes'];
        let loadedCount = 0;
        
        keys.forEach(key => {
            const storageKey = `${this.currentUserHash}_${key}`;
            const data = this.loadFromStorage(storageKey);
            if (data && Array.isArray(data) && data.length > 0) {
                this[key] = data;
                
                // 更新用户缓存
                if (!this.userDataCache.has(this.currentUserHash)) {
                    this.userDataCache.set(this.currentUserHash, { 
                        cacheTime: Date.now(),
                        data: {} 
                    });
                }
                this.userDataCache.get(this.currentUserHash).data[key] = data;
                loadedCount++;
            }
        });
        
        logger.info('AppState', '用户系统数据加载完成', {
            userHash: this.currentUserHash,
            loadedCount,
            totalKeys: keys.length
        });
    }

    /**
     * @function saveToStorage - 保存数据到本地存储
     * @description 安全地将数据保存到localStorage
     * @param {string} key - 存储键
     * @param {any} data - 数据
     */
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            logger.error('AppState', '数据存储失败', { key, error: error.message });
        }
    }

    /**
     * @function loadFromStorage - 从本地存储加载数据
     * @description 安全地从localStorage加载数据
     * @param {string} key - 存储键
     * @returns {any} 存储的数据
     */
    loadFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            logger.error('AppState', '数据加载失败', { key, error: error.message });
            return null;
        }
    }

    /**
     * @function getCacheInfo - 获取缓存信息
     * @description 获取当前缓存状态的详细信息，用于调试和监控
     * @returns {object} 缓存状态信息
     */
    getCacheInfo() {
        return {
            currentUserHash: this.currentUserHash,
            userCount: this.userDataCache.size,
            currentUserData: this.currentUserHash ?
                this.userDataCache.get(this.currentUserHash) : null,
            hasLegacyData: !this.currentUserHash && (
                this.backendUsers.length > 0 ||
                this.subCategories.length > 0 ||
                this.carTypes.length > 0
            )
        };
    }

    /**
     * @function validateDataIntegrity - 验证数据完整性
     * @description 检查系统数据的完整性和有效性
     * @returns {object} 验证结果
     */
    validateDataIntegrity() {
        const requiredKeys = ['backendUsers', 'subCategories', 'carTypes'];
        const validation = {
            isValid: true,
            missingData: [],
            emptyData: [],
            userHash: this.currentUserHash
        };

        requiredKeys.forEach(key => {
            const data = this.getUserSystemData(key) || this[key];
            if (!data) {
                validation.missingData.push(key);
                validation.isValid = false;
            } else if (Array.isArray(data) && data.length === 0) {
                validation.emptyData.push(key);
                validation.isValid = false;
            }
        });

        return validation;
    }
}

// 创建全局实例
window.appState = new AppState();

logger.info('模块', 'AppState模块加载完成', {
    version: 'v4.0.1',
    userHash: window.appState.currentUserHash
});
