/**
 * @file smart-selection-modular.js - 智能选择服务 (模块化重构版)
 * @description 实现基于订单内容的智能ID选择功能，包括车型识别和自动选择sub_category_id、car_type_id、incharge_by_backend_user_id
 * <AUTHOR> IDE
 * @created_at 2024-12-19
 * @updated_at 2025-01-06
 * @version 4.0.1 - 模块化重构版
 * 
 * 注意：此文件是模块化重构版本，主要功能已拆分到以下模块：
 * - core/smart-selection/main.js - 主服务类
 * - core/smart-selection/matching-engine.js - 增强匹配引擎
 * - core/smart-selection/learning-engine.js - 智能学习引擎
 * - core/smart-selection/accuracy-calculator.js - 动态精度计算器
 * - core/smart-selection/api-sync-manager.js - 动态API同步管理器
 * - core/smart-selection/index.js - 模块索引和初始化
 */

// 模块化加载状态
let modulesLoaded = false;
let loadingPromise = null;

/**
 * @function loadSmartSelectionModules - 加载智能选择模块
 * @returns {Promise<boolean>} 加载结果
 */
async function loadSmartSelectionModules() {
    if (modulesLoaded) {
        return true;
    }
    
    if (loadingPromise) {
        return loadingPromise;
    }
    
    loadingPromise = new Promise(async (resolve, reject) => {
        try {
            if (typeof window !== 'undefined') {
                // 浏览器环境 - 动态加载脚本
                const loadScript = (src) => {
                    return new Promise((resolve, reject) => {
                        // 检查脚本是否已经加载
                        const existingScript = document.querySelector(`script[src="${src}"]`);
                        if (existingScript) {
                            resolve();
                            return;
                        }
                        
                        const script = document.createElement('script');
                        script.src = src;
                        script.onload = resolve;
                        script.onerror = reject;
                        document.head.appendChild(script);
                    });
                };
                
                // 按依赖顺序加载模块
                await loadScript('core/smart-selection/api-sync-manager.js');
                await loadScript('core/smart-selection/matching-engine.js');
                await loadScript('core/smart-selection/learning-engine.js');
                await loadScript('core/smart-selection/accuracy-calculator.js');
                await loadScript('core/smart-selection/main.js');
                
                console.log('SmartSelection', '模块化智能选择服务加载完成');
                
                // 创建全局智能选择服务实例
                if (!window.smartSelection) {
                    window.smartSelection = new SmartSelectionService();
                    await window.smartSelection.initialize();
                }
                
                modulesLoaded = true;
                resolve(true);
                
            } else {
                // Node.js环境
                try {
                    const smartSelectionModules = require('./smart-selection/index');
                    
                    // 将模块导出到全局
                    global.DynamicApiSyncManager = smartSelectionModules.DynamicApiSyncManager;
                    global.EnhancedMatchingEngine = smartSelectionModules.EnhancedMatchingEngine;
                    global.IntelligentLearningEngine = smartSelectionModules.IntelligentLearningEngine;
                    global.DynamicAccuracyCalculator = smartSelectionModules.DynamicAccuracyCalculator;
                    global.SmartSelectionService = smartSelectionModules.SmartSelectionService;
                    
                    modulesLoaded = true;
                    resolve(true);
                } catch (error) {
                    console.error('SmartSelection', '模块化服务加载失败', error);
                    reject(error);
                }
            }
            
        } catch (error) {
            console.error('SmartSelection', '模块加载失败', error);
            reject(error);
        }
    });
    
    return loadingPromise;
}

/**
 * @function getSmartSelectionService - 获取智能选择服务实例
 * @returns {Promise<SmartSelectionService>} 智能选择服务实例
 */
async function getSmartSelectionService() {
    try {
        await loadSmartSelectionModules();
        
        if (typeof window !== 'undefined') {
            return window.smartSelection;
        } else {
            if (!global.smartSelectionInstance) {
                global.smartSelectionInstance = new global.SmartSelectionService();
                await global.smartSelectionInstance.initialize();
            }
            return global.smartSelectionInstance;
        }
    } catch (error) {
        console.error('SmartSelection', '获取服务实例失败', error);
        throw error;
    }
}

/**
 * @function checkModularSupport - 检查模块化支持
 * @returns {object} 支持状态
 */
function checkModularSupport() {
    const support = {
        environment: typeof window !== 'undefined' ? 'browser' : 'node',
        modulesLoaded: modulesLoaded,
        availableClasses: [],
        errors: []
    };
    
    try {
        if (typeof window !== 'undefined') {
            // 浏览器环境检查
            const classes = [
                'DynamicApiSyncManager',
                'EnhancedMatchingEngine', 
                'IntelligentLearningEngine',
                'DynamicAccuracyCalculator',
                'SmartSelectionService'
            ];
            
            classes.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    support.availableClasses.push(className);
                }
            });
            
            support.smartSelectionInstance = typeof window.smartSelection !== 'undefined';
        } else {
            // Node.js环境检查
            const classes = [
                'DynamicApiSyncManager',
                'EnhancedMatchingEngine',
                'IntelligentLearningEngine', 
                'DynamicAccuracyCalculator',
                'SmartSelectionService'
            ];
            
            classes.forEach(className => {
                if (typeof global[className] !== 'undefined') {
                    support.availableClasses.push(className);
                }
            });
            
            support.smartSelectionInstance = typeof global.smartSelectionInstance !== 'undefined';
        }
    } catch (error) {
        support.errors.push(error.message);
    }
    
    return support;
}

/**
 * @function initializeModularSmartSelection - 初始化模块化智能选择服务
 * @returns {Promise<object>} 初始化结果
 */
async function initializeModularSmartSelection() {
    try {
        console.log('SmartSelection', '开始初始化模块化智能选择服务');
        
        const startTime = Date.now();
        
        // 加载模块
        await loadSmartSelectionModules();
        
        // 获取服务实例
        const smartSelection = await getSmartSelectionService();
        
        // 检查支持状态
        const support = checkModularSupport();
        
        const initTime = Date.now() - startTime;
        
        const result = {
            success: true,
            initTime: initTime,
            version: '4.0.1',
            support: support,
            smartSelection: smartSelection,
            message: '模块化智能选择服务初始化成功'
        };
        
        console.log('SmartSelection', '模块化智能选择服务初始化完成', result);
        
        return result;
        
    } catch (error) {
        console.error('SmartSelection', '模块化智能选择服务初始化失败', error);
        
        return {
            success: false,
            error: error.message,
            message: '模块化智能选择服务初始化失败，请检查模块文件'
        };
    }
}

// 导出函数
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.loadSmartSelectionModules = loadSmartSelectionModules;
    window.getSmartSelectionService = getSmartSelectionService;
    window.checkModularSupport = checkModularSupport;
    window.initializeModularSmartSelection = initializeModularSmartSelection;
    
    // 自动初始化
    document.addEventListener('DOMContentLoaded', () => {
        initializeModularSmartSelection().then(result => {
            if (result.success) {
                console.log('SmartSelection', '自动初始化成功');
            } else {
                console.warn('SmartSelection', '自动初始化失败，请手动调用 initializeModularSmartSelection()');
            }
        });
    });
    
} else {
    // Node.js环境
    module.exports = {
        loadSmartSelectionModules,
        getSmartSelectionService,
        checkModularSupport,
        initializeModularSmartSelection
    };
}

// 版本信息
const MODULAR_VERSION = {
    version: '4.0.1',
    buildDate: '2025-01-06',
    description: '模块化重构版智能选择服务',
    modules: [
        'api-sync-manager.js',
        'matching-engine.js', 
        'learning-engine.js',
        'accuracy-calculator.js',
        'main.js',
        'index.js'
    ]
};

if (typeof window !== 'undefined') {
    window.SMART_SELECTION_MODULAR_VERSION = MODULAR_VERSION;
} else {
    module.exports.MODULAR_VERSION = MODULAR_VERSION;
}

console.log('SmartSelection', '模块化入口点加载完成', MODULAR_VERSION);
